#!/usr/bin/env python3
"""
Simple test script to verify tensor parallel functionality.
"""

import os
import sys
import torch

# Add the project root to the path
base_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, base_dir)

def test_simple_tensor_parallel():
    """Test the simple tensor parallel implementation."""
    print("Testing Simple Tensor Parallel Implementation...")
    
    try:
        from protenix.model.simple_tensor_parallel import (
            SimpleTensorParallelLinear,
            SimpleTensorParallelWrapper,
            SimpleHybridTensorParallelWrapper
        )
        print("✓ Simple tensor parallel imports successful")
    except Exception as e:
        print(f"✗ Simple tensor parallel import failed: {e}")
        return False
    
    # Test SimpleTensorParallelLinear
    try:
        original_linear = torch.nn.Linear(128, 64)
        tp_linear = SimpleTensorParallelLinear(original_linear, tp_size=2, rank=0)
        
        # Test forward pass
        x = torch.randn(1, 10, 128)
        output = tp_linear(x)
        
        print(f"✓ SimpleTensorParallelLinear test passed")
        print(f"  Input shape: {x.shape}")
        print(f"  Output shape: {output.shape}")
        
        return True
    except Exception as e:
        print(f"✗ SimpleTensorParallelLinear test failed: {e}")
        return False

def test_config_imports():
    """Test that configs can be imported."""
    try:
        from configs.configs_base import configs as configs_base
        print("✓ Base configs import successful")
        
        # Check if our new configs are there
        if "enable_tensor_parallel" in configs_base:
            print("✓ enable_tensor_parallel found in configs")
        else:
            print("✗ enable_tensor_parallel NOT found in configs")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Config import failed: {e}")
        return False

def test_environment_setup():
    """Test environment variable setup."""
    # Set test environment variables
    os.environ["ENABLE_TENSOR_PARALLEL"] = "true"
    os.environ["TENSOR_PARALLEL_SIZE"] = "2"
    
    enable_tp = os.environ.get("ENABLE_TENSOR_PARALLEL", "false").lower() == "true"
    tp_size = int(os.environ.get("TENSOR_PARALLEL_SIZE", "1"))
    
    if enable_tp and tp_size == 2:
        print("✓ Environment variable setup successful")
        return True
    else:
        print("✗ Environment variable setup failed")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Simple Tensor Parallel Test")
    print("=" * 60)
    
    all_tests_passed = True
    
    # Test 1: Environment setup
    if not test_environment_setup():
        all_tests_passed = False
    
    # Test 2: Config imports
    if not test_config_imports():
        all_tests_passed = False
    
    # Test 3: Simple tensor parallel
    if not test_simple_tensor_parallel():
        all_tests_passed = False
    
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("✓ All tests passed! Tensor parallel should work.")
    else:
        print("✗ Some tests failed. Check the errors above.")
        sys.exit(1)
    print("=" * 60)
