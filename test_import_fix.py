#!/usr/bin/env python3
"""
Test script to verify that the tensor parallel import fix works.
"""

import sys
import os

# Add the project root to the path
base_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, base_dir)

def test_imports():
    """Test all the imports that were failing."""
    print("Testing tensor parallel imports...")
    
    try:
        from protenix.model.simple_tensor_parallel import (
            SimpleTensorParallelWrapper, 
            SimpleHybridTensorParallelWrapper,
            SimpleTensorParallelLinear
        )
        print("✓ Simple tensor parallel imports successful")
    except Exception as e:
        print(f"✗ Simple tensor parallel import failed: {e}")
        return False
    
    try:
        # Test the original tensor parallel import (should fallback gracefully)
        from protenix.model.tensor_parallel import TensorParallelWrapper
        print("✓ Original tensor parallel imports successful (or fallback working)")
    except Exception as e:
        print(f"✗ Original tensor parallel import failed: {e}")
        return False
    
    try:
        # Test that we can import the distributed utils
        from protenix.utils.distributed import DIST_WRAPPER
        print("✓ Distributed utils import successful")
    except Exception as e:
        print(f"✗ Distributed utils import failed: {e}")
        return False
    
    return True

def test_simple_tensor_parallel():
    """Test the simple tensor parallel functionality."""
    import torch
    import torch.nn as nn
    from protenix.model.simple_tensor_parallel import SimpleTensorParallelLinear
    
    print("\nTesting SimpleTensorParallelLinear...")
    
    # Create a test linear layer
    original_linear = nn.Linear(128, 64)
    
    # Create tensor parallel version (simulating rank 0 of 2)
    tp_linear = SimpleTensorParallelLinear(original_linear, tp_size=2, rank=0)
    
    # Test forward pass
    x = torch.randn(1, 10, 128)
    output = tp_linear(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    print("✓ SimpleTensorParallelLinear test passed")

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Tensor Parallel Import Fix")
    print("=" * 50)
    
    if test_imports():
        print("\n✓ All imports successful!")
        test_simple_tensor_parallel()
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some imports failed!")
        sys.exit(1)
