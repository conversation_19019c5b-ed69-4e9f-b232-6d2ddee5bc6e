#!/bin/bash
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# AlphaFold3 Inference with Tensor Parallel Optimization
# This script runs inference with both hybrid parallel and tensor parallel enabled

PROJECT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
echo "Project directory: ${PROJECT_DIR}"

# Check if GPU ID is provided
if [ -z "$1" ]; then
  echo "Error: GPU ID not provided"
  echo "Usage: $0 <GPU_ID> [tensor_parallel_size]"
  echo "Example: $0 0,1 2        # Use GPUs 0,1 with tensor parallel size 2"
  echo "Example: $0 0,1,2,3 4    # Use GPUs 0,1,2,3 with tensor parallel size 4"
  exit 1
fi

# Parse GPU IDs and determine tensor parallel size
export CUDA_VISIBLE_DEVICES=$1
IFS=',' read -ra GPU_ARRAY <<< "$1"
GPU_COUNT=${#GPU_ARRAY[@]}

# Set tensor parallel size (default to GPU count if not specified)
TENSOR_PARALLEL_SIZE=${2:-$GPU_COUNT}

echo "Using GPUs: $1"
echo "GPU count: $GPU_COUNT"
echo "Tensor parallel size: $TENSOR_PARALLEL_SIZE"

# Environment variables
export LAYERNORM_TYPE=fast_layernorm
export USE_DEEPSPEED_EVO_ATTTENTION=true

# Model checkpoint path
load_checkpoint_path="/jfs/yakun-li/yakun_li_genbio_ai/data/protenix/release_model/model_v0.2.0.pt"

# Inference parameters
N_sample=5
N_step=200
N_cycle=10
seed=101
use_deepspeed_evo_attention=true
input_json_path=${PROJECT_DIR}/examples/example.json

# Output directory with timestamp for profiling results
timestamp=$(date +"%Y%m%d_%H%M%S")
dump_dir="./outputs/output_tensor_parallel_${timestamp}"

echo "Starting inference with Tensor Parallel optimization..."
echo "Results will be saved to: ${dump_dir}"
echo "Timing information will be in: ${dump_dir}/timing/"

# Validate tensor parallel size
if [ $TENSOR_PARALLEL_SIZE -gt $GPU_COUNT ]; then
    echo "Warning: Tensor parallel size ($TENSOR_PARALLEL_SIZE) is larger than available GPUs ($GPU_COUNT)"
    echo "Setting tensor parallel size to $GPU_COUNT"
    TENSOR_PARALLEL_SIZE=$GPU_COUNT
fi

# Set environment variables for tensor parallel
export ENABLE_TENSOR_PARALLEL=true
export TENSOR_PARALLEL_SIZE=${TENSOR_PARALLEL_SIZE}

# Run the profiler script with tensor parallel enabled
torchrun --nproc_per_node=$GPU_COUNT ${PROJECT_DIR}/runner/inference_profiler_tensor_parallel.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step}

echo "Inference with Tensor Parallel completed."
echo "Check timing results in: ${dump_dir}/timing/timing_summary.json"
echo ""
echo "Performance comparison:"
echo "- Hybrid parallel only: Use bin/inference_profiler_demo.sh"
echo "- Hybrid + Tensor parallel: Use bin/inference_tensor_parallel_demo.sh"
