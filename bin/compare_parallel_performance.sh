#!/bin/bash
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Performance comparison script for Hybrid vs Hybrid+Tensor Parallel
# This script runs both modes and compares the performance

PROJECT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
echo "Project directory: ${PROJECT_DIR}"

# Check if GPU ID is provided
if [ -z "$1" ]; then
  echo "Error: GPU ID not provided"
  echo "Usage: $0 <GPU_ID> [tensor_parallel_size]"
  echo "Example: $0 0,1 2        # Compare hybrid vs hybrid+tensor parallel"
  echo "Example: $0 0,1,2,3 4    # Compare with 4 GPUs"
  exit 1
fi

# Parse GPU IDs and determine tensor parallel size
export CUDA_VISIBLE_DEVICES=$1
IFS=',' read -ra GPU_ARRAY <<< "$1"
GPU_COUNT=${#GPU_ARRAY[@]}
TENSOR_PARALLEL_SIZE=${2:-$GPU_COUNT}

echo "=== Performance Comparison Setup ==="
echo "Using GPUs: $1"
echo "GPU count: $GPU_COUNT"
echo "Tensor parallel size: $TENSOR_PARALLEL_SIZE"
echo ""

# Environment variables
export LAYERNORM_TYPE=fast_layernorm
export USE_DEEPSPEED_EVO_ATTTENTION=true

# Model checkpoint path
load_checkpoint_path="/jfs/yakun-li/yakun_li_genbio_ai/data/protenix/release_model/model_v0.2.0.pt"

# Inference parameters
N_sample=5
N_step=200
N_cycle=10
seed=101
input_json_path=${PROJECT_DIR}/examples/example.json

# Output directories with timestamp
timestamp=$(date +"%Y%m%d_%H%M%S")
dump_dir_hybrid="./outputs/comparison_hybrid_${timestamp}"
dump_dir_tensor="./outputs/comparison_tensor_${timestamp}"

echo "=== Running Hybrid Parallel Only ==="
echo "Results will be saved to: ${dump_dir_hybrid}"

# Run hybrid parallel only
torchrun --nproc_per_node=$GPU_COUNT ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir_hybrid} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step} \
--enable_hybrid_parallel true \
--enable_tensor_parallel false

echo ""
echo "=== Running Hybrid + Tensor Parallel ==="
echo "Results will be saved to: ${dump_dir_tensor}"

# Run hybrid + tensor parallel
torchrun --nproc_per_node=$GPU_COUNT ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir_tensor} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step} \
--enable_hybrid_parallel true \
--enable_tensor_parallel true \
--tensor_parallel_size ${TENSOR_PARALLEL_SIZE}

echo ""
echo "=== Performance Comparison Results ==="

# Function to extract timing from JSON
extract_timing() {
    local json_file=$1
    local metric=$2
    if [ -f "$json_file" ]; then
        python3 -c "
import json
import sys
try:
    with open('$json_file', 'r') as f:
        data = json.load(f)
    if 'samples' in data and len(data['samples']) > 0:
        timing = data['samples'][0]['timing']
        if '$metric' in timing:
            print(f'{timing[\"$metric\"]:.3f}')
        else:
            print('N/A')
    else:
        print('N/A')
except:
    print('N/A')
"
    else
        echo "N/A"
    fi
}

# Extract timing information
hybrid_summary="${dump_dir_hybrid}/timing/timing_summary.json"
tensor_summary="${dump_dir_tensor}/timing/timing_summary.json"

echo "Timing Comparison:"
echo "=================="

metrics=("total_inference" "pairformer" "diffusion" "confidence" "model_forward")

for metric in "${metrics[@]}"; do
    hybrid_time=$(extract_timing "$hybrid_summary" "$metric")
    tensor_time=$(extract_timing "$tensor_summary" "$metric")
    
    echo -n "$metric: "
    echo -n "Hybrid=${hybrid_time}s, "
    echo -n "Tensor=${tensor_time}s"
    
    # Calculate speedup if both times are available
    if [ "$hybrid_time" != "N/A" ] && [ "$tensor_time" != "N/A" ]; then
        speedup=$(python3 -c "
try:
    h = float('$hybrid_time')
    t = float('$tensor_time')
    if t > 0:
        print(f' (Speedup: {h/t:.2f}x)')
    else:
        print(' (Speedup: N/A)')
except:
    print(' (Speedup: N/A)')
")
        echo "$speedup"
    else
        echo " (Speedup: N/A)"
    fi
done

echo ""
echo "Detailed results:"
echo "- Hybrid parallel only: ${dump_dir_hybrid}/timing/"
echo "- Hybrid + Tensor parallel: ${dump_dir_tensor}/timing/"
echo ""
echo "To view detailed timing:"
echo "cat ${hybrid_summary}"
echo "cat ${tensor_summary}"
