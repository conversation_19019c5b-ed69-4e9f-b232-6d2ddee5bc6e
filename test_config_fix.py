#!/usr/bin/env python3
"""
Test script to verify that the configuration fix works.
"""

import sys
import os

def test_config_parsing():
    """Test that the new tensor parallel configs can be parsed."""
    try:
        # Test importing the config system
        from protenix.config import parse_configs
        from configs.configs_base import configs as configs_base
        
        print("✓ Config system imports successful")
        
        # Test that our new configs are in the base configs
        if "enable_tensor_parallel" in configs_base:
            print("✓ enable_tensor_parallel found in base configs")
        else:
            print("✗ enable_tensor_parallel NOT found in base configs")
            return False
            
        if "tensor_parallel_size" in configs_base:
            print("✓ tensor_parallel_size found in base configs")
        else:
            print("✗ tensor_parallel_size NOT found in base configs")
            return False
        
        # Test parsing with tensor parallel arguments
        test_arg_str = "--enable_tensor_parallel true --tensor_parallel_size 2"
        
        try:
            parsed_configs = parse_configs(
                configs=configs_base,
                arg_str=test_arg_str,
                fill_required_with_null=True,
            )
            print("✓ Config parsing with tensor parallel args successful")
            print(f"  enable_tensor_parallel: {parsed_configs.enable_tensor_parallel}")
            print(f"  tensor_parallel_size: {parsed_configs.tensor_parallel_size}")
            return True
        except Exception as e:
            print(f"✗ Config parsing failed: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Config system import failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Configuration Fix")
    print("=" * 50)
    
    if test_config_parsing():
        print("\n✓ Configuration fix successful!")
    else:
        print("\n✗ Configuration fix failed!")
        sys.exit(1)
