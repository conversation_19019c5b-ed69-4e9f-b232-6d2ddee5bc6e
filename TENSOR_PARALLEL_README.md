# AlphaFold3 Tensor Parallel 优化实现

## 概述

本实现在现有的混合并行化（Hybrid Parallel）基础上，额外增加了Tensor Parallel优化，进一步加速AlphaFold3推理过程。

### 优化策略

1. **现有混合并行**：Pairformer只在rank 0计算一次，Diffusion模块在所有rank并行运行
2. **新增Tensor Parallel**：在pairformer之后、diffusion之前的模块（distogram_head、confidence_head等）进行tensor parallel优化

## 架构设计

### 核心组件

1. **TensorParallelWrapper** (`protenix/model/tensor_parallel.py`)
   - 管理tensor parallel的执行和通信
   - 对线性层进行列并行（ColwiseParallel）分片
   - 处理all-gather通信以重建完整输出

2. **HybridTensorParallelWrapper** (`protenix/model/tensor_parallel.py`)
   - 结合混合并行和tensor parallel的统一接口
   - 协调两种并行策略的执行

3. **EnhancedHybridParallelWrapper** (`protenix/model/hybrid_parallel.py`)
   - 扩展现有的混合并行wrapper
   - 支持tensor parallel的集成

### 优化的模块

1. **DistogramHead**
   - 对线性层进行tensor parallel
   - 输出维度分片，使用all-gather重建

2. **ConfidenceHead**
   - 对内部多个线性层进行tensor parallel
   - 包括pairformer stack的并行化

3. **其他线性层**
   - `linear_no_bias_sinit`
   - `linear_no_bias_zinit1/zinit2`
   - `linear_no_bias_token_bond`

## 使用方法

### 1. 配置启用

```python
from configs.tensor_parallel_config import update_configs_for_tensor_parallel

# 更新配置以启用tensor parallel
configs = update_configs_for_tensor_parallel(base_configs)
configs["enable_tensor_parallel"] = True
configs["tensor_parallel_size"] = 4  # 使用4个GPU
```

### 2. 模型初始化

```python
from protenix.model.protenix import Protenix

# 创建模型（会自动启用tensor parallel）
model = Protenix(configs)
```

### 3. 推理运行

```python
# 正常运行推理，tensor parallel会自动生效
prediction, _, _ = model(
    input_feature_dict=data["input_feature_dict"],
    label_full_dict=None,
    label_dict=None,
    mode="inference",
)
```

## 配置选项

### 基本配置

```python
tensor_parallel_configs = {
    "enable_tensor_parallel": True,        # 启用tensor parallel
    "tensor_parallel_size": None,          # GPU数量（None=使用所有GPU）
    "enable_hybrid_parallel": True,        # 同时启用混合并行
}
```

### 高级配置

```python
# 模块选择
"tensor_parallel_modules": {
    "distogram_head": True,
    "confidence_head": True,
    "linear_layers": True,
}

# 通信设置
"tensor_parallel_communication": {
    "use_all_gather": True,
    "backend": "nccl",
    "overlap_comm": True,
}

# 内存优化
"tensor_parallel_memory": {
    "memory_efficient_attention": True,
    "clear_cache_between_modules": True,
}
```

## 性能建议

### 不同蛋白质大小的推荐配置

1. **小型蛋白质** (< 1000 tokens)
   - 推荐：`hybrid_2gpu`
   - 原因：tensor parallel开销可能不值得

2. **中型蛋白质** (1000-3000 tokens)
   - 推荐：`hybrid_tensor_4gpu`
   - 原因：并行化和效率的良好平衡

3. **大型蛋白质** (> 3000 tokens)
   - 推荐：`hybrid_tensor_8gpu`
   - 原因：大型蛋白质的最大并行化

### 示例配置

```python
from configs.tensor_parallel_config import example_configs

# 2 GPU混合+tensor parallel
config_2gpu = example_configs["hybrid_tensor_2gpu"]

# 4 GPU混合+tensor parallel
config_4gpu = example_configs["hybrid_tensor_4gpu"]

# 8 GPU混合+tensor parallel
config_8gpu = example_configs["hybrid_tensor_8gpu"]
```

## 测试和验证

### 运行测试

```bash
# 单GPU测试
python test_tensor_parallel.py

# 多GPU测试
torchrun --nproc_per_node=4 test_tensor_parallel.py
```

### 性能对比

测试脚本会自动比较：
- 常规模型 vs Tensor Parallel模型
- 不同蛋白质大小的性能
- 内存使用情况

## 技术细节

### Tensor Parallel策略

1. **列并行（ColwiseParallel）**
   - 将线性层的权重矩阵按列分片
   - 每个GPU计算部分输出
   - 使用all-gather重建完整输出

2. **通信优化**
   - 最小化all-gather操作
   - 重用广播的pairformer输出
   - 异步通信（可选）

### 内存优化

1. **梯度检查点**：减少中间激活的内存占用
2. **缓存清理**：模块间自动清理GPU缓存
3. **内存高效注意力**：使用优化的注意力实现

## 故障排除

### 常见问题

1. **CUDA OOM错误**
   - 减少`tensor_parallel_size`
   - 启用`gradient_checkpointing`
   - 减少`chunk_size`

2. **通信超时**
   - 检查NCCL配置
   - 确保所有GPU在同一节点
   - 增加通信超时时间

3. **性能没有提升**
   - 确保蛋白质足够大
   - 检查GPU间通信带宽
   - 尝试不同的`tp_size`

### 调试选项

```python
debug_configs = {
    "enable_tp_logging": True,           # 详细日志
    "profile_tp_communication": True,    # 通信性能分析
    "validate_tp_outputs": True,         # 输出验证
    "monitor_memory": True,              # 内存监控
}
```

## 兼容性

- **PyTorch版本**：>= 2.0
- **CUDA版本**：>= 11.8
- **GPU要求**：支持NCCL的NVIDIA GPU
- **内存要求**：每GPU至少24GB（大型蛋白质）

## 未来改进

1. **流水线并行**：结合pipeline parallel进一步优化
2. **动态负载均衡**：根据蛋白质大小动态调整并行策略
3. **混合精度优化**：FP16/BF16支持
4. **跨节点扩展**：支持多节点tensor parallel
