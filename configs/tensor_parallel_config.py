# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Configuration for Tensor Parallel optimization in AlphaFold3.

This configuration enables tensor parallelization for post-pairformer modules
to further accelerate inference beyond the existing hybrid parallel strategy.
"""

# Tensor Parallel Configuration
tensor_parallel_configs = {
    # Enable tensor parallelization
    "enable_tensor_parallel": True,
    
    # Tensor parallel size (number of GPUs to use for tensor parallelism)
    # If None, will use all available GPUs
    "tensor_parallel_size": None,
    
    # Enable hybrid parallelization (existing feature)
    "enable_hybrid_parallel": True,
    
    # Modules to apply tensor parallelization
    "tensor_parallel_modules": {
        "distogram_head": True,
        "confidence_head": True,
        "linear_layers": True,
    },
    
    # Communication settings
    "tensor_parallel_communication": {
        # Use all-gather for output reconstruction
        "use_all_gather": True,
        
        # Communication backend
        "backend": "nccl",
        
        # Overlap computation and communication
        "overlap_comm": True,
    },
    
    # Memory optimization settings
    "tensor_parallel_memory": {
        # Enable memory efficient attention
        "memory_efficient_attention": True,
        
        # Gradient checkpointing for tensor parallel modules
        "gradient_checkpointing": False,
        
        # Clear cache between modules
        "clear_cache_between_modules": True,
    },
    
    # Performance tuning
    "tensor_parallel_performance": {
        # Chunk size for tensor parallel operations
        "chunk_size": 4,
        
        # Use inplace operations when safe
        "inplace_safe": True,
        
        # Enable async tensor parallel operations
        "async_tp": False,
    },
}

# Integration with existing configs
def update_configs_for_tensor_parallel(base_configs):
    """
    Update base configurations to enable tensor parallel features.
    
    Args:
        base_configs: Base configuration dictionary
        
    Returns:
        Updated configuration dictionary
    """
    # Create a copy to avoid modifying the original
    updated_configs = base_configs.copy()
    
    # Add tensor parallel configurations
    updated_configs.update(tensor_parallel_configs)
    
    # Ensure compatibility with existing hybrid parallel
    if updated_configs.get("enable_tensor_parallel", False):
        # Tensor parallel works best with hybrid parallel enabled
        updated_configs["enable_hybrid_parallel"] = True
        
        # Adjust batch sizes for tensor parallel
        if "diffusion_batch_size" in updated_configs:
            # Reduce batch size per rank when using tensor parallel
            tp_size = updated_configs.get("tensor_parallel_size", 1) or 1
            updated_configs["diffusion_batch_size"] = max(
                1, updated_configs["diffusion_batch_size"] // tp_size
            )
    
    return updated_configs

# Example usage configurations
example_configs = {
    # Single GPU (no parallelization)
    "single_gpu": {
        "enable_hybrid_parallel": False,
        "enable_tensor_parallel": False,
    },
    
    # 2 GPUs with hybrid parallel only
    "hybrid_2gpu": {
        "enable_hybrid_parallel": True,
        "enable_tensor_parallel": False,
    },
    
    # 2 GPUs with both hybrid and tensor parallel
    "hybrid_tensor_2gpu": {
        "enable_hybrid_parallel": True,
        "enable_tensor_parallel": True,
        "tensor_parallel_size": 2,
    },
    
    # 4 GPUs with both hybrid and tensor parallel
    "hybrid_tensor_4gpu": {
        "enable_hybrid_parallel": True,
        "enable_tensor_parallel": True,
        "tensor_parallel_size": 4,
    },
    
    # 8 GPUs with both hybrid and tensor parallel
    "hybrid_tensor_8gpu": {
        "enable_hybrid_parallel": True,
        "enable_tensor_parallel": True,
        "tensor_parallel_size": 8,
    },
}

# Performance recommendations
performance_recommendations = {
    "small_proteins": {
        # For proteins with < 1000 tokens
        "recommended_config": "hybrid_2gpu",
        "notes": "Tensor parallel overhead may not be worth it for small proteins"
    },
    
    "medium_proteins": {
        # For proteins with 1000-3000 tokens
        "recommended_config": "hybrid_tensor_4gpu",
        "notes": "Good balance of parallelization and efficiency"
    },
    
    "large_proteins": {
        # For proteins with > 3000 tokens
        "recommended_config": "hybrid_tensor_8gpu",
        "notes": "Maximum parallelization for large proteins"
    },
}

# Debugging and monitoring
debug_configs = {
    # Enable detailed logging for tensor parallel operations
    "enable_tp_logging": False,
    
    # Profile tensor parallel communication
    "profile_tp_communication": False,
    
    # Validate tensor parallel outputs against single GPU
    "validate_tp_outputs": False,
    
    # Monitor memory usage
    "monitor_memory": True,
}
