# AlphaFold3 推理脚本使用指南

## 概述

现在有三种推理脚本可供选择，分别对应不同的并行化策略：

1. **原始脚本**：`bin/inference_profiler_demo.sh` - 仅使用混合并行
2. **Tensor Parallel脚本**：`bin/inference_tensor_parallel_demo.sh` - 混合并行 + Tensor Parallel
3. **性能对比脚本**：`bin/compare_parallel_performance.sh` - 同时运行两种模式进行对比

**重要更新**：修复了PyTorch版本兼容性问题，现在使用简化的tensor parallel实现，无需依赖PyTorch的高级tensor parallel功能。

## 使用方法

### 1. 原始混合并行（推荐用于小型蛋白质）

```bash
# 使用2个GPU，仅混合并行
bash bin/inference_profiler_demo.sh 0,1
```

**特点**：
- Pairformer只在rank 0计算一次
- Diffusion模块在所有rank并行运行
- 适合小型蛋白质（< 1000 tokens）

### 2. 混合并行 + Tensor Parallel（推荐用于大型蛋白质）

```bash
# 使用2个GPU，混合并行 + Tensor Parallel
bash bin/inference_tensor_parallel_demo.sh 0,1 2

# 使用4个GPU，混合并行 + Tensor Parallel
bash bin/inference_tensor_parallel_demo.sh 0,1,2,3 4

# 自动检测GPU数量作为tensor parallel size
bash bin/inference_tensor_parallel_demo.sh 0,1,2,3
```

**特点**：
- 在混合并行基础上增加Tensor Parallel
- 对distogram_head和confidence_head进行tensor parallel优化
- 适合大型蛋白质（> 1000 tokens）

### 3. 性能对比（用于评估优化效果）

```bash
# 对比2个GPU的性能
bash bin/compare_parallel_performance.sh 0,1 2

# 对比4个GPU的性能
bash bin/compare_parallel_performance.sh 0,1,2,3 4
```

**特点**：
- 自动运行两种模式
- 生成详细的性能对比报告
- 计算加速比

## 参数说明

### GPU配置
- `0,1`：使用GPU 0和1
- `0,1,2,3`：使用GPU 0、1、2、3
- 确保GPU之间有高速互连（如NVLink）

### Tensor Parallel Size
- 第二个参数指定tensor parallel的大小
- 通常等于GPU数量
- 可以小于GPU数量（如4个GPU使用2的tensor parallel size）

## 输出结果

### 目录结构
```
outputs/
├── output_profiler_YYYYMMDD_HHMMSS/     # 原始混合并行结果
├── output_tensor_parallel_YYYYMMDD_HHMMSS/  # Tensor parallel结果
└── comparison_*/                         # 性能对比结果
    ├── timing/
    │   ├── timing_summary.json         # 汇总时间信息
    │   └── sample_name_timing.json     # 单个样本时间信息
    └── ...
```

### 时间信息
- `total_inference`：总推理时间
- `pairformer`：Pairformer计算时间
- `diffusion`：Diffusion采样时间
- `confidence`：Confidence head计算时间
- `model_forward`：模型前向传播总时间

## 性能建议

### 根据蛋白质大小选择策略

| 蛋白质大小 | 推荐脚本 | GPU配置 | 原因 |
|-----------|---------|---------|------|
| < 1000 tokens | `inference_profiler_demo.sh` | 2 GPU | Tensor parallel开销可能不值得 |
| 1000-3000 tokens | `inference_tensor_parallel_demo.sh` | 4 GPU | 并行化和效率的良好平衡 |
| > 3000 tokens | `inference_tensor_parallel_demo.sh` | 8 GPU | 最大并行化效果 |

### 硬件要求
- **GPU内存**：每GPU至少24GB（大型蛋白质）
- **GPU互连**：NVLink或高速PCIe
- **CUDA版本**：>= 11.8
- **PyTorch版本**：>= 2.0

## 故障排除

### 常见问题

1. **CUDA OOM错误**
   ```bash
   # 减少样本数量
   # 修改脚本中的 N_sample=5 为更小值
   ```

2. **通信超时**
   ```bash
   # 检查GPU配置
   nvidia-smi topo -m
   
   # 确保NCCL正常工作
   export NCCL_DEBUG=INFO
   ```

3. **性能没有提升**
   - 确保蛋白质足够大（> 1000 tokens）
   - 检查GPU间通信带宽
   - 尝试不同的tensor parallel size

### 调试选项

在脚本中添加调试环境变量：
```bash
export NCCL_DEBUG=INFO          # NCCL调试信息
export CUDA_LAUNCH_BLOCKING=1   # 同步CUDA操作
```

## 示例命令

### 快速测试
```bash
# 2 GPU快速测试
bash bin/inference_tensor_parallel_demo.sh 0,1

# 性能对比测试
bash bin/compare_parallel_performance.sh 0,1
```

### 生产环境
```bash
# 4 GPU生产推理
bash bin/inference_tensor_parallel_demo.sh 0,1,2,3 4

# 8 GPU大规模推理
bash bin/inference_tensor_parallel_demo.sh 0,1,2,3,4,5,6,7 8
```

## 监控和分析

### 查看时间信息
```bash
# 查看汇总时间
cat outputs/output_tensor_parallel_*/timing/timing_summary.json

# 分析性能瓶颈
python -c "
import json
with open('outputs/output_tensor_parallel_*/timing/timing_summary.json') as f:
    data = json.load(f)
    timing = data['samples'][0]['timing']
    for k, v in timing.items():
        print(f'{k}: {v:.3f}s')
"
```

### GPU使用率监控
```bash
# 实时监控GPU使用率
watch -n 1 nvidia-smi

# 监控GPU通信
nvidia-smi nvlink -s
```

这个指南应该能帮助您选择合适的推理脚本并获得最佳性能！
