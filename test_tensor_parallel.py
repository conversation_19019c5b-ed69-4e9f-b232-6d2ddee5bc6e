#!/usr/bin/env python3
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Test script for Tensor Parallel implementation in AlphaFold3.

This script tests the tensor parallel functionality and compares
performance with and without tensor parallelization.
"""

import os
import sys
import time
import torch
import torch.distributed as dist
import logging
from typing import Dict, Any

# Add the project root to the path
base_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, base_dir)

from configs.configs_base import configs as configs_base
from configs.configs_data import data_configs
from configs.configs_inference import inference_configs
from configs.tensor_parallel_config import update_configs_for_tensor_parallel, example_configs
from protenix.config import parse_configs
from protenix.model.protenix import Protenix
from protenix.utils.distributed import DIST_WRAPPER
from protenix.utils.seed import seed_everything

logger = logging.getLogger(__name__)


class TensorParallelTester:
    """Test class for tensor parallel functionality."""
    
    def __init__(self, configs):
        self.configs = configs
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def setup_distributed(self):
        """Setup distributed environment for testing."""
        if DIST_WRAPPER.world_size > 1:
            dist.init_process_group(backend="nccl")
            torch.cuda.set_device(DIST_WRAPPER.local_rank)
            
    def create_test_model(self, enable_tp: bool = False):
        """Create a test model with or without tensor parallel."""
        # Update configs for tensor parallel if enabled
        if enable_tp:
            test_configs = update_configs_for_tensor_parallel(self.configs)
            test_configs["enable_tensor_parallel"] = True
        else:
            test_configs = self.configs.copy()
            test_configs["enable_tensor_parallel"] = False
            test_configs["enable_hybrid_parallel"] = False
            
        # Create model
        model = Protenix(test_configs).to(self.device)
        model.eval()
        
        return model
        
    def create_test_data(self, n_tokens: int = 100, n_atoms: int = 500):
        """Create synthetic test data."""
        batch_size = 1
        
        # Create synthetic input features
        input_feature_dict = {
            "residue_index": torch.randint(0, 20, (batch_size, n_tokens), device=self.device),
            "asym_id": torch.zeros(batch_size, n_tokens, dtype=torch.long, device=self.device),
            "has_frame": torch.ones(batch_size, n_tokens, dtype=torch.bool, device=self.device),
            "atom_to_token_idx": torch.randint(0, n_tokens, (n_atoms,), device=self.device),
            "is_ligand": torch.zeros(n_atoms, dtype=torch.bool, device=self.device),
            "token_bonds": torch.zeros(batch_size, n_tokens, n_tokens, device=self.device),
            "msa": torch.randn(batch_size, 64, n_tokens, 49, device=self.device),
            "has_deletion": torch.zeros(batch_size, 64, n_tokens, dtype=torch.bool, device=self.device),
            "deletion_value": torch.zeros(batch_size, 64, n_tokens, device=self.device),
            "profile": torch.randn(batch_size, n_tokens, 25, device=self.device),
            "deletion_mean": torch.zeros(batch_size, n_tokens, device=self.device),
        }
        
        return input_feature_dict
        
    def test_model_forward(self, model, input_data, test_name: str):
        """Test model forward pass and measure performance."""
        logger.info(f"Testing {test_name}...")
        
        # Warm up
        with torch.no_grad():
            for _ in range(3):
                try:
                    _ = model.get_pairformer_output(
                        input_feature_dict=input_data,
                        N_cycle=1,
                        inplace_safe=True,
                        chunk_size=4,
                    )
                except Exception as e:
                    logger.warning(f"Warmup failed: {e}")
                    break
                    
        # Measure performance
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        start_time = time.time()
        
        try:
            with torch.no_grad():
                s_inputs, s_trunk, z_trunk = model.get_pairformer_output(
                    input_feature_dict=input_data,
                    N_cycle=1,
                    inplace_safe=True,
                    chunk_size=4,
                )
                
                # Test distogram head
                if hasattr(model, 'distogram_head'):
                    distogram_logits = model.distogram_head(z_trunk)
                    
                # Test confidence head (simplified)
                if hasattr(model, 'confidence_head'):
                    # Create dummy coordinates for confidence head
                    n_atoms = input_data["atom_to_token_idx"].shape[0]
                    dummy_coords = torch.randn(1, 1, n_atoms, 3, device=self.device)
                    
                    confidence_outputs = model.run_confidence_head(
                        input_feature_dict=input_data,
                        s_inputs=s_inputs,
                        s_trunk=s_trunk,
                        z_trunk=z_trunk,
                        pair_mask=None,
                        x_pred_coords=dummy_coords,
                        use_memory_efficient_kernel=False,
                        use_deepspeed_evo_attention=False,
                        use_lma=False,
                        inplace_safe=True,
                        chunk_size=4,
                    )
                    
        except Exception as e:
            logger.error(f"Forward pass failed for {test_name}: {e}")
            return None, float('inf')
            
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        end_time = time.time()
        
        elapsed_time = end_time - start_time
        logger.info(f"{test_name} completed in {elapsed_time:.3f} seconds")
        
        return {
            "s_inputs_shape": s_inputs.shape,
            "s_trunk_shape": s_trunk.shape,
            "z_trunk_shape": z_trunk.shape,
        }, elapsed_time
        
    def run_comparison_test(self, n_tokens: int = 100):
        """Run comparison test between regular and tensor parallel models."""
        logger.info(f"Running comparison test with {n_tokens} tokens...")
        
        # Create test data
        input_data = self.create_test_data(n_tokens=n_tokens)
        
        # Test regular model
        regular_model = self.create_test_model(enable_tp=False)
        regular_results, regular_time = self.test_model_forward(
            regular_model, input_data, "Regular Model"
        )
        
        # Test tensor parallel model (only if multiple GPUs available)
        if DIST_WRAPPER.world_size > 1:
            tp_model = self.create_test_model(enable_tp=True)
            tp_results, tp_time = self.test_model_forward(
                tp_model, input_data, "Tensor Parallel Model"
            )
            
            # Compare results
            if regular_results and tp_results:
                logger.info("=== Performance Comparison ===")
                logger.info(f"Regular Model Time: {regular_time:.3f}s")
                logger.info(f"Tensor Parallel Time: {tp_time:.3f}s")
                if tp_time > 0:
                    speedup = regular_time / tp_time
                    logger.info(f"Speedup: {speedup:.2f}x")
                else:
                    logger.info("Speedup: N/A (TP model failed)")
        else:
            logger.info("Skipping tensor parallel test (single GPU)")
            
        # Cleanup
        del regular_model
        if DIST_WRAPPER.world_size > 1:
            del tp_model
        torch.cuda.empty_cache() if torch.cuda.is_available() else None


def main():
    """Main test function."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Setup configs
    configs = {**configs_base, **{"data": data_configs}, **inference_configs}
    
    # Parse any command line arguments
    configs = parse_configs(
        configs=configs,
        arg_str="",  # No additional args for testing
        fill_required_with_null=True,
    )
    
    # Set seed for reproducibility
    seed_everything(seed=42, deterministic=True)
    
    # Create tester
    tester = TensorParallelTester(configs)
    
    # Setup distributed if needed
    tester.setup_distributed()
    
    # Run tests with different protein sizes
    test_sizes = [50, 100, 200] if DIST_WRAPPER.world_size > 1 else [50, 100]
    
    for n_tokens in test_sizes:
        try:
            tester.run_comparison_test(n_tokens=n_tokens)
        except Exception as e:
            logger.error(f"Test failed for {n_tokens} tokens: {e}")
            
    logger.info("All tests completed!")


if __name__ == "__main__":
    main()
