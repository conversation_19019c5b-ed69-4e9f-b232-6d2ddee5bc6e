# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Simple Tensor Parallel implementation for AlphaFold3 that doesn't rely on PyTorch's tensor parallel.

This is a fallback implementation that manually handles tensor parallelization
using basic distributed operations.
"""

import torch
import torch.distributed as dist
import torch.nn as nn
from typing import Any, Dict, Optional, Tuple, List
from protenix.utils.distributed import DIST_WRAPPER


class SimpleTensorParallelLinear(nn.Module):
    """
    A simple tensor parallel linear layer using manual sharding.
    """
    
    def __init__(self, original_linear: nn.Linear, tp_size: int, rank: int):
        super().__init__()
        self.tp_size = tp_size
        self.rank = rank
        self.original_linear = original_linear
        
        # Shard the weight matrix along the output dimension
        out_features = original_linear.out_features
        in_features = original_linear.in_features
        
        # Calculate shard size
        self.shard_size = out_features // tp_size
        start_idx = rank * self.shard_size
        end_idx = start_idx + self.shard_size
        
        # Create sharded linear layer
        self.linear = nn.Linear(in_features, self.shard_size, bias=original_linear.bias is not None)
        
        # Copy the sharded weights
        with torch.no_grad():
            self.linear.weight.copy_(original_linear.weight[start_idx:end_idx])
            if original_linear.bias is not None:
                self.linear.bias.copy_(original_linear.bias[start_idx:end_idx])
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with all-gather."""
        # Compute local output
        local_output = self.linear(x)
        
        # All-gather to reconstruct full output
        if self.tp_size > 1 and dist.is_initialized():
            gathered_outputs = [torch.zeros_like(local_output) for _ in range(self.tp_size)]
            dist.all_gather(gathered_outputs, local_output)
            output = torch.cat(gathered_outputs, dim=-1)
        else:
            output = local_output
            
        return output


class SimpleTensorParallelWrapper:
    """
    Simple tensor parallel wrapper that doesn't depend on PyTorch's tensor parallel.
    """
    
    def __init__(self, model, enable_tensor_parallel: bool = True, tp_size: int = None):
        """
        Initialize the simple tensor parallel wrapper.
        
        Args:
            model: The Protenix model
            enable_tensor_parallel: Whether to enable tensor parallelization
            tp_size: Tensor parallel size (defaults to world_size)
        """
        self.model = model
        self.enable_tensor_parallel = enable_tensor_parallel and DIST_WRAPPER.world_size > 1
        self.rank = DIST_WRAPPER.rank
        self.world_size = DIST_WRAPPER.world_size
        self.tp_size = tp_size or self.world_size
        
        if self.enable_tensor_parallel:
            self._setup_simple_tensor_parallel()
    
    def _setup_simple_tensor_parallel(self):
        """Setup simple tensor parallel by replacing linear layers."""
        try:
            # Replace distogram head linear layer
            if hasattr(self.model, 'distogram_head') and hasattr(self.model.distogram_head, 'linear'):
                original_linear = self.model.distogram_head.linear
                self.model.distogram_head.linear = SimpleTensorParallelLinear(
                    original_linear, self.tp_size, self.rank
                )
                print(f"Rank {self.rank}: Replaced distogram head linear layer with tensor parallel version")
            
            # Replace confidence head linear layers
            if hasattr(self.model, 'confidence_head'):
                confidence_head = self.model.confidence_head
                linear_layers = [
                    'linear_no_bias_s1', 'linear_no_bias_s2', 'linear_no_bias_d',
                    'linear_no_bias_pae', 'linear_no_bias_pde'
                ]
                
                for layer_name in linear_layers:
                    if hasattr(confidence_head, layer_name):
                        original_linear = getattr(confidence_head, layer_name)
                        if isinstance(original_linear, nn.Linear):
                            setattr(confidence_head, layer_name, SimpleTensorParallelLinear(
                                original_linear, self.tp_size, self.rank
                            ))
                            print(f"Rank {self.rank}: Replaced {layer_name} with tensor parallel version")
            
            print(f"Rank {self.rank}: Simple tensor parallel setup completed")
            
        except Exception as e:
            print(f"Warning: Failed to setup simple tensor parallel: {e}")
            self.enable_tensor_parallel = False


class SimpleHybridTensorParallelWrapper:
    """
    Simple version of hybrid tensor parallel wrapper.
    """
    
    def __init__(self, model, enable_hybrid: bool = True, enable_tp: bool = True, tp_size: int = None):
        """
        Initialize the simple hybrid tensor parallel wrapper.
        
        Args:
            model: The Protenix model
            enable_hybrid: Whether to enable hybrid parallelization
            enable_tp: Whether to enable tensor parallelization
            tp_size: Tensor parallel size
        """
        self.model = model
        self.enable_hybrid = enable_hybrid and DIST_WRAPPER.world_size > 1
        self.enable_tp = enable_tp and DIST_WRAPPER.world_size > 1
        self.rank = DIST_WRAPPER.rank
        self.world_size = DIST_WRAPPER.world_size
        self.tp_size = tp_size or self.world_size
        
        # Initialize simple tensor parallel wrapper
        if self.enable_tp:
            self.tensor_parallel = SimpleTensorParallelWrapper(model, enable_tp, tp_size)
        else:
            self.tensor_parallel = None
    
    def run_post_pairformer_with_tp(
        self,
        s_inputs: torch.Tensor,
        s_trunk: torch.Tensor,
        z_trunk: torch.Tensor,
        input_feature_dict: Dict[str, Any],
        pred_dict: Dict[str, torch.Tensor],
        **kwargs
    ) -> Dict[str, torch.Tensor]:
        """
        Run post-pairformer modules with simple tensor parallelization.
        
        Args:
            s_inputs: Single embeddings from input
            s_trunk: Single embeddings from pairformer
            z_trunk: Pair embeddings from pairformer
            input_feature_dict: Input features
            pred_dict: Prediction dictionary to update
            **kwargs: Additional arguments
            
        Returns:
            Updated prediction dictionary
        """
        # Distogram computation (with tensor parallel if enabled)
        distogram_logits = self.model.distogram_head(z_trunk)
        
        # Compute contact probabilities
        from protenix.model import sample_confidence
        pred_dict["contact_probs"] = sample_confidence.compute_contact_prob(
            distogram_logits=distogram_logits,
            **sample_confidence.get_bin_params(self.model.configs.loss.distogram),
        )
        
        # Confidence computation (with tensor parallel if enabled)
        N_token = input_feature_dict["residue_index"].shape[-1]
        deepspeed_evo_attention_condition_satisfy = N_token > 16
        
        (
            pred_dict["plddt"],
            pred_dict["pae"],
            pred_dict["pde"],
            pred_dict["resolved"],
        ) = self.model.run_confidence_head(
            input_feature_dict=input_feature_dict,
            s_inputs=s_inputs,
            s_trunk=s_trunk,
            z_trunk=z_trunk,
            pair_mask=None,
            x_pred_coords=pred_dict["coordinate"],
            **kwargs
        )
        
        return pred_dict


def apply_simple_tensor_parallel_to_model(model, enable_tp: bool = True, tp_size: int = None):
    """
    Apply simple tensor parallelization to the model's post-pairformer modules.
    
    Args:
        model: The Protenix model
        enable_tp: Whether to enable tensor parallelization
        tp_size: Tensor parallel size
        
    Returns:
        SimpleTensorParallelWrapper instance
    """
    return SimpleTensorParallelWrapper(model, enable_tp, tp_size)
