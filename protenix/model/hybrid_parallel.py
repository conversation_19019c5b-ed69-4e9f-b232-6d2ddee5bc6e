# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Hybrid Parallelization for AlphaFold3 Inference

This module implements a hybrid parallelization strategy where:
1. Pairformer and other pre-diffusion modules compute only once on rank 0
2. Results are broadcasted to all ranks
3. Diffusion module runs in data parallel mode across all ranks
"""

import torch
import torch.distributed as dist
from typing import Any, Dict, Optional, Tuple
from protenix.utils.distributed import DIST_WRAPPER


class EnhancedHybridParallelWrapper:
    """
    Enhanced wrapper that combines hybrid parallelization with tensor parallelization.

    This class manages:
    1. Hybrid parallel execution (pairformer on rank 0, diffusion on all ranks)
    2. Tensor parallel execution for post-pairformer modules
    3. Optimized communication patterns
    """

    def __init__(self, model, enable_hybrid_parallel: bool = True, enable_tensor_parallel: bool = False, tp_size: int = None):
        """
        Initialize the enhanced hybrid parallel wrapper.

        Args:
            model: The Protenix model
            enable_hybrid_parallel: Whether to enable hybrid parallelization
            enable_tensor_parallel: Whether to enable tensor parallelization
            tp_size: Tensor parallel size
        """
        self.model = model
        self.enable_hybrid_parallel = enable_hybrid_parallel and DIST_WRAPPER.world_size > 1
        self.enable_tensor_parallel = enable_tensor_parallel and DIST_WRAPPER.world_size > 1
        self.rank = DIST_WRAPPER.rank
        self.world_size = DIST_WRAPPER.world_size
        self.tp_size = tp_size or self.world_size

        # Initialize tensor parallel wrapper if enabled
        if self.enable_tensor_parallel:
            from .tensor_parallel import TensorParallelWrapper
            self.tensor_parallel = TensorParallelWrapper(model, enable_tensor_parallel, tp_size)
        else:
            self.tensor_parallel = None

    def enhanced_hybrid_inference_loop(
        self,
        input_feature_dict: Dict[str, Any],
        N_cycle: int,
        N_sample: int,
        noise_schedule: Any,
        inplace_safe: bool = True,
        chunk_size: Optional[int] = 4,
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Enhanced hybrid parallel inference loop with tensor parallelization.

        This method orchestrates both hybrid and tensor parallel execution:
        1. Compute pairformer on rank 0 and broadcast results
        2. Run diffusion in parallel on all ranks
        3. Run post-pairformer modules with tensor parallelization

        Args:
            input_feature_dict: Input features
            N_cycle: Number of recycling cycles
            N_sample: Number of samples per rank
            noise_schedule: Noise schedule for diffusion
            inplace_safe: Whether to use inplace operations
            chunk_size: Chunk size for memory efficiency

        Returns:
            Tuple of (coordinates, additional_outputs)
        """
        # Step 1: Compute pairformer results (shared computation)
        s_inputs, s_trunk, z_trunk = self.compute_pairformer_shared(
            input_feature_dict=input_feature_dict,
            N_cycle=N_cycle,
            inplace_safe=inplace_safe,
            chunk_size=chunk_size,
        )

        # Step 2: Run diffusion in parallel
        coordinates = self.run_diffusion_parallel(
            input_feature_dict=input_feature_dict,
            s_inputs=s_inputs,
            s_trunk=s_trunk,
            z_trunk=z_trunk,
            N_sample=N_sample,
            noise_schedule=noise_schedule,
            inplace_safe=inplace_safe,
        )

        # Step 3: Run post-pairformer modules with tensor parallelization
        additional_outputs = {}
        if self.enable_tensor_parallel and self.tensor_parallel:
            # Use tensor parallel for distogram and confidence heads
            additional_outputs = self._run_post_pairformer_with_tp(
                s_inputs=s_inputs,
                s_trunk=s_trunk,
                z_trunk=z_trunk,
                input_feature_dict=input_feature_dict,
                coordinates=coordinates,
                inplace_safe=inplace_safe,
                chunk_size=chunk_size,
            )
        else:
            # Use regular computation
            additional_outputs = self._run_post_pairformer_regular(
                s_inputs=s_inputs,
                s_trunk=s_trunk,
                z_trunk=z_trunk,
                input_feature_dict=input_feature_dict,
                coordinates=coordinates,
                inplace_safe=inplace_safe,
                chunk_size=chunk_size,
            )

        return coordinates, additional_outputs

    def _run_post_pairformer_with_tp(
        self,
        s_inputs: torch.Tensor,
        s_trunk: torch.Tensor,
        z_trunk: torch.Tensor,
        input_feature_dict: Dict[str, Any],
        coordinates: torch.Tensor,
        inplace_safe: bool = True,
        chunk_size: Optional[int] = 4,
    ) -> Dict[str, torch.Tensor]:
        """
        Run post-pairformer modules with tensor parallelization.
        """
        outputs = {}

        # Distogram computation with tensor parallelization
        distogram_logits = self.model.distogram_head(z_trunk)

        # Compute contact probabilities
        from protenix.model import sample_confidence
        outputs["contact_probs"] = sample_confidence.compute_contact_prob(
            distogram_logits=distogram_logits,
            **sample_confidence.get_bin_params(self.model.configs.loss.distogram),
        )

        # Confidence computation with tensor parallelization
        N_token = input_feature_dict["residue_index"].shape[-1]
        deepspeed_evo_attention_condition_satisfy = N_token > 16

        (
            outputs["plddt"],
            outputs["pae"],
            outputs["pde"],
            outputs["resolved"],
        ) = self.model.run_confidence_head(
            input_feature_dict=input_feature_dict,
            s_inputs=s_inputs,
            s_trunk=s_trunk,
            z_trunk=z_trunk,
            pair_mask=None,
            x_pred_coords=coordinates,
            use_memory_efficient_kernel=self.model.configs.use_memory_efficient_kernel,
            use_deepspeed_evo_attention=self.model.configs.use_deepspeed_evo_attention
            and deepspeed_evo_attention_condition_satisfy,
            use_lma=self.model.configs.use_lma,
            inplace_safe=inplace_safe,
            chunk_size=chunk_size,
        )

        return outputs

    def _run_post_pairformer_regular(
        self,
        s_inputs: torch.Tensor,
        s_trunk: torch.Tensor,
        z_trunk: torch.Tensor,
        input_feature_dict: Dict[str, Any],
        coordinates: torch.Tensor,
        inplace_safe: bool = True,
        chunk_size: Optional[int] = 4,
    ) -> Dict[str, torch.Tensor]:
        """
        Run post-pairformer modules without tensor parallelization.
        """
        outputs = {}

        # Regular distogram computation
        distogram_logits = self.model.distogram_head(z_trunk)

        # Compute contact probabilities
        from protenix.model import sample_confidence
        outputs["contact_probs"] = sample_confidence.compute_contact_prob(
            distogram_logits=distogram_logits,
            **sample_confidence.get_bin_params(self.model.configs.loss.distogram),
        )

        # Regular confidence computation
        N_token = input_feature_dict["residue_index"].shape[-1]
        deepspeed_evo_attention_condition_satisfy = N_token > 16

        (
            outputs["plddt"],
            outputs["pae"],
            outputs["pde"],
            outputs["resolved"],
        ) = self.model.run_confidence_head(
            input_feature_dict=input_feature_dict,
            s_inputs=s_inputs,
            s_trunk=s_trunk,
            z_trunk=z_trunk,
            pair_mask=None,
            x_pred_coords=coordinates,
            use_memory_efficient_kernel=self.model.configs.use_memory_efficient_kernel,
            use_deepspeed_evo_attention=self.model.configs.use_deepspeed_evo_attention
            and deepspeed_evo_attention_condition_satisfy,
            use_lma=self.model.configs.use_lma,
            inplace_safe=inplace_safe,
            chunk_size=chunk_size,
        )

        return outputs


class HybridParallelWrapper:
    """
    Wrapper for hybrid parallelization of AlphaFold3 inference.
    
    This class manages the execution of pairformer on rank 0 only,
    broadcasts the results, and then runs diffusion in data parallel mode.
    """
    
    def __init__(self, model, enable_hybrid_parallel: bool = True):
        """
        Initialize the hybrid parallel wrapper.
        
        Args:
            model: The Protenix model
            enable_hybrid_parallel: Whether to enable hybrid parallelization
        """
        self.model = model
        self.enable_hybrid_parallel = enable_hybrid_parallel and DIST_WRAPPER.world_size > 1
        self.rank = DIST_WRAPPER.rank
        self.world_size = DIST_WRAPPER.world_size
        
    def _broadcast_tensor(self, tensor: torch.Tensor, src_rank: int = 0) -> torch.Tensor:
        """
        Broadcast tensor from source rank to all ranks.

        Args:
            tensor: Tensor to broadcast
            src_rank: Source rank (default: 0)

        Returns:
            Broadcasted tensor
        """
        if not self.enable_hybrid_parallel:
            return tensor

        if tensor is None:
            return None

        # First broadcast the tensor metadata (shape, dtype)
        if self.rank == src_rank:
            # Send tensor metadata
            metadata = {
                'shape': list(tensor.shape),
                'dtype': tensor.dtype,
                'device_type': tensor.device.type
            }
        else:
            metadata = None

        # Broadcast metadata
        metadata_list = [metadata]
        dist.broadcast_object_list(metadata_list, src=src_rank)
        metadata = metadata_list[0]

        # Create tensor with correct shape on non-source ranks
        if self.rank != src_rank:
            device = next(self.model.parameters()).device
            tensor = torch.empty(
                metadata['shape'],
                dtype=metadata['dtype'],
                device=device
            )

        # Broadcast the actual tensor data
        dist.broadcast(tensor, src=src_rank)
        return tensor
    
    def _broadcast_tensor_dict(self, tensor_dict: Dict[str, torch.Tensor], src_rank: int = 0) -> Dict[str, torch.Tensor]:
        """
        Broadcast a dictionary of tensors from source rank to all ranks.
        
        Args:
            tensor_dict: Dictionary of tensors to broadcast
            src_rank: Source rank (default: 0)
            
        Returns:
            Dictionary with broadcasted tensors
        """
        if not self.enable_hybrid_parallel:
            return tensor_dict
            
        result = {}
        for key, tensor in tensor_dict.items():
            if tensor is not None:
                result[key] = self._broadcast_tensor(tensor, src_rank)
            else:
                result[key] = None
        return result
    
    def compute_pairformer_shared(
        self,
        input_feature_dict: Dict[str, Any],
        N_cycle: int,
        inplace_safe: bool = True,
        chunk_size: Optional[int] = 4,
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute pairformer results on rank 0 and broadcast to all ranks.
        
        Args:
            input_feature_dict: Input features
            N_cycle: Number of recycling cycles
            inplace_safe: Whether to use inplace operations
            chunk_size: Chunk size for memory efficiency
            
        Returns:
            Tuple of (s_inputs, s_trunk, z_trunk)
        """
        if not self.enable_hybrid_parallel:
            # Fallback to normal computation on all ranks
            return self._compute_pairformer_local(
                input_feature_dict, N_cycle, inplace_safe, chunk_size
            )
        
        if self.rank == 0:
            # Only rank 0 computes pairformer
            s_inputs, s_trunk, z_trunk = self._compute_pairformer_local(
                input_feature_dict, N_cycle, inplace_safe, chunk_size
            )
        else:
            # Other ranks create placeholder tensors
            # We need to get the shapes from rank 0 first
            s_inputs, s_trunk, z_trunk = self._create_placeholder_tensors(input_feature_dict)
        
        # Broadcast results from rank 0 to all ranks
        s_inputs = self._broadcast_tensor(s_inputs, src_rank=0)
        s_trunk = self._broadcast_tensor(s_trunk, src_rank=0)
        z_trunk = self._broadcast_tensor(z_trunk, src_rank=0)
        
        return s_inputs, s_trunk, z_trunk
    
    def _compute_pairformer_local(
        self,
        input_feature_dict: Dict[str, Any],
        N_cycle: int,
        inplace_safe: bool = True,
        chunk_size: Optional[int] = 4,
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Local computation of pairformer (runs on rank 0 only in hybrid mode).

        This method replicates the exact logic from get_pairformer_output.
        """
        # Directly call the model's get_pairformer_output method
        # This ensures we use the exact same logic as the original implementation
        return self.model.get_pairformer_output(
            input_feature_dict=input_feature_dict,
            N_cycle=N_cycle,
            inplace_safe=inplace_safe,
            chunk_size=chunk_size,
        )
    
    def _create_placeholder_tensors(
        self, input_feature_dict: Dict[str, Any]
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Create placeholder tensors with correct shapes for non-rank-0 processes.

        The actual shapes will be determined during the broadcast process.
        """
        device = next(self.model.parameters()).device

        # Create minimal dummy tensors - shapes will be corrected during broadcast
        s_inputs = torch.empty(1, device=device)
        s_trunk = torch.empty(1, device=device)
        z_trunk = torch.empty(1, device=device)

        return s_inputs, s_trunk, z_trunk
    
    def run_diffusion_parallel(
        self,
        input_feature_dict: Dict[str, Any],
        s_inputs: torch.Tensor,
        s_trunk: torch.Tensor,
        z_trunk: torch.Tensor,
        N_sample: int,
        noise_schedule: Any,
        inplace_safe: bool = True,
    ) -> torch.Tensor:
        """
        Run diffusion module in data parallel mode.

        Each rank will generate different samples due to different random seeds,
        providing natural data parallelism for the diffusion sampling process.

        Args:
            input_feature_dict: Input features
            s_inputs: Single input embeddings (broadcasted from rank 0)
            s_trunk: Single trunk embeddings from pairformer (broadcasted from rank 0)
            z_trunk: Pair trunk embeddings from pairformer (broadcasted from rank 0)
            N_sample: Number of samples per rank
            noise_schedule: Noise schedule for diffusion
            inplace_safe: Whether to use inplace operations

        Returns:
            Sampled coordinates from this rank
        """
        # Set different random seed for each rank to ensure different samples
        if self.enable_hybrid_parallel:
            # Each rank gets a different seed based on its rank
            torch.manual_seed(torch.initial_seed() + self.rank)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(torch.initial_seed() + self.rank)

        # Run diffusion sampling - each rank will generate different samples
        coordinates = self.model.sample_diffusion(
            denoise_net=self.model.diffusion_module,
            input_feature_dict=input_feature_dict,
            s_inputs=s_inputs,
            s_trunk=s_trunk,
            z_trunk=z_trunk,
            N_sample=N_sample,
            noise_schedule=noise_schedule,
            inplace_safe=inplace_safe,
        )

        return coordinates

    def hybrid_inference_loop(
        self,
        input_feature_dict: Dict[str, Any],
        N_cycle: int,
        N_sample: int,
        noise_schedule: Any,
        inplace_safe: bool = True,
        chunk_size: Optional[int] = 4,
    ) -> torch.Tensor:
        """
        Main hybrid parallel inference loop.

        This method orchestrates the hybrid parallel execution:
        1. Compute pairformer on rank 0 and broadcast results
        2. Run diffusion in parallel on all ranks

        Args:
            input_feature_dict: Input features
            N_cycle: Number of recycling cycles
            N_sample: Number of samples per rank
            noise_schedule: Noise schedule for diffusion
            inplace_safe: Whether to use inplace operations
            chunk_size: Chunk size for memory efficiency

        Returns:
            Sampled coordinates from this rank
        """
        # Step 1: Compute pairformer results (shared computation)
        s_inputs, s_trunk, z_trunk = self.compute_pairformer_shared(
            input_feature_dict=input_feature_dict,
            N_cycle=N_cycle,
            inplace_safe=inplace_safe,
            chunk_size=chunk_size,
        )

        # Step 2: Run diffusion in parallel
        coordinates = self.run_diffusion_parallel(
            input_feature_dict=input_feature_dict,
            s_inputs=s_inputs,
            s_trunk=s_trunk,
            z_trunk=z_trunk,
            N_sample=N_sample,
            noise_schedule=noise_schedule,
            inplace_safe=inplace_safe,
        )

        return coordinates
