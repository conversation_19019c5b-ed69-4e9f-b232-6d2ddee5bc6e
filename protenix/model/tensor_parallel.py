# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Tensor Parallel implementation for AlphaFold3 post-pairformer modules.

This module implements tensor parallelization for modules that run after
pairformer but before diffusion, specifically targeting:
1. DistogramHead
2. ConfidenceHead
3. Other linear layers

The goal is to further accelerate inference by parallelizing these computations
across multiple GPUs while maintaining the existing hybrid parallel strategy.
"""

import torch
import torch.distributed as dist
import torch.nn as nn
from typing import Any, Dict, Optional, Tuple, List
from protenix.utils.distributed import DIST_WRAPPER

# Try to import tensor parallel components with version compatibility
try:
    from torch.distributed.device_mesh import Device<PERSON><PERSON>
    from torch.distributed.tensor import Replicate, <PERSON>hard, DTensor
    from torch.distributed.tensor.parallel import (
        ColwiseParallel,
        RowwiseParallel,
        parallelize_module,
    )
    TENSOR_PARALLEL_AVAILABLE = True
except ImportError:
    try:
        # Try alternative import paths for different PyTorch versions
        from torch.distributed.device_mesh import DeviceMesh
        from torch.distributed._tensor import Replicate, Shard, DTensor
        from torch.distributed._tensor.parallel import (
            ColwiseParallel,
            RowwiseParallel,
            parallelize_module,
        )
        TENSOR_PARALLEL_AVAILABLE = True
    except ImportError:
        # Fallback: create dummy classes if tensor parallel is not available
        print("Warning: Tensor parallel not available in this PyTorch version. Using fallback implementation.")
        TENSOR_PARALLEL_AVAILABLE = False

        class DeviceMesh:
            def __init__(self, *args, **kwargs):
                pass
            def size(self):
                return 1
            def get_group(self):
                return None

        class Replicate:
            pass

        class Shard:
            def __init__(self, *args, **kwargs):
                pass

        class DTensor:
            pass

        class ColwiseParallel:
            def __init__(self, *args, **kwargs):
                pass

        class RowwiseParallel:
            def __init__(self, *args, **kwargs):
                pass

        def parallelize_module(*args, **kwargs):
            print("Warning: parallelize_module not available, skipping tensor parallelization")
            pass


class TensorParallelWrapper:
    """
    Wrapper for tensor parallelization of AlphaFold3 post-pairformer modules.
    
    This class manages the tensor parallel execution of distogram_head,
    confidence_head, and other linear layers that run after pairformer
    but before diffusion.
    """
    
    def __init__(self, model, enable_tensor_parallel: bool = True, tp_size: int = None):
        """
        Initialize the tensor parallel wrapper.

        Args:
            model: The Protenix model
            enable_tensor_parallel: Whether to enable tensor parallelization
            tp_size: Tensor parallel size (defaults to world_size)
        """
        self.model = model
        self.enable_tensor_parallel = (
            enable_tensor_parallel
            and DIST_WRAPPER.world_size > 1
            and TENSOR_PARALLEL_AVAILABLE
        )
        self.rank = DIST_WRAPPER.rank
        self.world_size = DIST_WRAPPER.world_size
        self.tp_size = tp_size or self.world_size

        if not TENSOR_PARALLEL_AVAILABLE and enable_tensor_parallel:
            print("Warning: Tensor parallel requested but not available. Falling back to regular computation.")

        if self.enable_tensor_parallel:
            self._setup_tensor_parallel()
    
    def _setup_tensor_parallel(self):
        """Setup tensor parallel device mesh and apply parallelization."""
        if not TENSOR_PARALLEL_AVAILABLE:
            print("Warning: Tensor parallel not available, skipping setup")
            return

        try:
            # Create device mesh for tensor parallelism
            device_ids = list(range(self.tp_size))
            self.tp_mesh = DeviceMesh("cuda", device_ids)

            # Apply tensor parallelization to target modules
            self._parallelize_distogram_head()
            self._parallelize_confidence_head()
            self._parallelize_linear_layers()
        except Exception as e:
            print(f"Warning: Failed to setup tensor parallel: {e}")
            self.enable_tensor_parallel = False
    
    def _parallelize_distogram_head(self):
        """Apply tensor parallelization to distogram head."""
        if not TENSOR_PARALLEL_AVAILABLE:
            return

        if hasattr(self.model, 'distogram_head'):
            try:
                # Parallelize the linear layer in distogram head
                parallelize_plan = {
                    "linear": ColwiseParallel(
                        input_layouts=Replicate(),
                        output_layouts=Shard(-1),  # Shard along the last dimension (no_bins)
                    )
                }

                parallelize_module(
                    module=self.model.distogram_head,
                    device_mesh=self.tp_mesh,
                    parallelize_plan=parallelize_plan,
                )
            except Exception as e:
                print(f"Warning: Failed to parallelize distogram head: {e}")
    
    def _parallelize_confidence_head(self):
        """Apply tensor parallelization to confidence head."""
        if not TENSOR_PARALLEL_AVAILABLE:
            return

        if hasattr(self.model, 'confidence_head'):
            try:
                # Parallelize linear layers in confidence head
                parallelize_plan = {
                    "linear_no_bias_s1": ColwiseParallel(
                        input_layouts=Replicate(),
                        output_layouts=Shard(-1),
                    ),
                    "linear_no_bias_s2": ColwiseParallel(
                        input_layouts=Replicate(),
                        output_layouts=Shard(-1),
                    ),
                    "linear_no_bias_d": ColwiseParallel(
                        input_layouts=Replicate(),
                        output_layouts=Shard(-1),
                    ),
                    "linear_no_bias_pae": ColwiseParallel(
                        input_layouts=Replicate(),
                        output_layouts=Shard(-1),
                    ),
                    "linear_no_bias_pde": ColwiseParallel(
                        input_layouts=Replicate(),
                        output_layouts=Shard(-1),
                    ),
                }

                parallelize_module(
                    module=self.model.confidence_head,
                    device_mesh=self.tp_mesh,
                    parallelize_plan=parallelize_plan,
                )
            except Exception as e:
                print(f"Warning: Failed to parallelize confidence head: {e}")
    
    def _parallelize_linear_layers(self):
        """Apply tensor parallelization to other linear layers in the model."""
        # Parallelize linear layers in the main model
        parallelize_plan = {
            "linear_no_bias_sinit": ColwiseParallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1),
            ),
            "linear_no_bias_zinit1": ColwiseParallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1),
            ),
            "linear_no_bias_zinit2": ColwiseParallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1),
            ),
            "linear_no_bias_token_bond": ColwiseParallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1),
            ),
        }
        
        # Apply to the main model (excluding already parallelized modules)
        for name, module in self.model.named_children():
            if name in ["distogram_head", "confidence_head"]:
                continue  # Skip already parallelized modules
            
            if hasattr(module, 'named_parameters'):
                # Check if this module has linear layers to parallelize
                module_plan = {}
                for param_name, _ in module.named_parameters():
                    if any(linear_name in param_name for linear_name in parallelize_plan.keys()):
                        layer_name = param_name.split('.')[0]
                        if layer_name in parallelize_plan:
                            module_plan[layer_name] = parallelize_plan[layer_name]
                
                if module_plan:
                    parallelize_module(
                        module=module,
                        device_mesh=self.tp_mesh,
                        parallelize_plan=module_plan,
                    )


class TensorParallelDistogramHead(nn.Module):
    """
    Tensor parallel version of DistogramHead with explicit all-gather.
    """
    
    def __init__(self, original_head, tp_mesh: DeviceMesh):
        super().__init__()
        self.original_head = original_head
        self.tp_mesh = tp_mesh
        self.tp_size = tp_mesh.size()
        self.rank = DIST_WRAPPER.rank
    
    def forward(self, z: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with tensor parallelization.
        
        Args:
            z: pair embedding [*, N_token, N_token, C_z]
            
        Returns:
            distogram logits [*, N_token, N_token, no_bins]
        """
        # Apply the parallelized linear layer
        logits_sharded = self.original_head.linear(z)  # [*, N_token, N_token, no_bins/tp_size]
        
        # All-gather to reconstruct full output
        logits = self._all_gather_last_dim(logits_sharded)  # [*, N_token, N_token, no_bins]
        
        # Apply symmetry operation
        logits = logits + logits.transpose(-2, -3)
        
        return logits
    
    def _all_gather_last_dim(self, tensor: torch.Tensor) -> torch.Tensor:
        """All-gather tensor along the last dimension."""
        if not self.tp_mesh or self.tp_size == 1:
            return tensor
        
        # Get the shape and create output tensor
        shape = list(tensor.shape)
        shape[-1] *= self.tp_size
        output = torch.empty(shape, dtype=tensor.dtype, device=tensor.device)
        
        # All-gather
        gathered_tensors = [torch.empty_like(tensor) for _ in range(self.tp_size)]
        dist.all_gather(gathered_tensors, tensor, group=self.tp_mesh.get_group())
        
        # Concatenate along last dimension
        output = torch.cat(gathered_tensors, dim=-1)
        
        return output


class TensorParallelConfidenceHead(nn.Module):
    """
    Tensor parallel version of ConfidenceHead with explicit communication.
    """
    
    def __init__(self, original_head, tp_mesh: DeviceMesh):
        super().__init__()
        self.original_head = original_head
        self.tp_mesh = tp_mesh
        self.tp_size = tp_mesh.size()
        self.rank = DIST_WRAPPER.rank
    
    def forward(self, *args, **kwargs) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass with tensor parallelization.
        
        Returns:
            Tuple of (plddt, pae, pde, resolved) predictions
        """
        # Use the memory efficient forward method
        if hasattr(self.original_head, 'memory_efficient_forward'):
            results = self.original_head.memory_efficient_forward(*args, **kwargs)
        else:
            results = self.original_head.forward(*args, **kwargs)
        
        # All-gather results if they are sharded
        gathered_results = []
        for result in results:
            if self._is_sharded(result):
                gathered_result = self._all_gather_last_dim(result)
                gathered_results.append(gathered_result)
            else:
                gathered_results.append(result)
        
        return tuple(gathered_results)
    
    def _is_sharded(self, tensor: torch.Tensor) -> bool:
        """Check if tensor is sharded (simple heuristic)."""
        # This is a simple heuristic - in practice, you might want to track
        # which tensors are sharded more explicitly
        return hasattr(tensor, '_tensor_parallel_sharded') or self.tp_size > 1
    
    def _all_gather_last_dim(self, tensor: torch.Tensor) -> torch.Tensor:
        """All-gather tensor along the last dimension."""
        if not self.tp_mesh or self.tp_size == 1:
            return tensor
        
        # Get the shape and create output tensor
        shape = list(tensor.shape)
        shape[-1] *= self.tp_size
        
        # All-gather
        gathered_tensors = [torch.empty_like(tensor) for _ in range(self.tp_size)]
        dist.all_gather(gathered_tensors, tensor, group=self.tp_mesh.get_group())
        
        # Concatenate along last dimension
        output = torch.cat(gathered_tensors, dim=-1)
        
        return output


def apply_tensor_parallel_to_model(model, enable_tp: bool = True, tp_size: int = None):
    """
    Apply tensor parallelization to the model's post-pairformer modules.

    Args:
        model: The Protenix model
        enable_tp: Whether to enable tensor parallelization
        tp_size: Tensor parallel size

    Returns:
        TensorParallelWrapper instance
    """
    return TensorParallelWrapper(model, enable_tp, tp_size)


def create_tp_device_mesh(tp_size: int) -> DeviceMesh:
    """
    Create a device mesh for tensor parallelism.

    Args:
        tp_size: Tensor parallel size

    Returns:
        DeviceMesh for tensor parallelism
    """
    device_ids = list(range(tp_size))
    return DeviceMesh("cuda", device_ids)


class TensorParallelLinearLayer(nn.Module):
    """
    A tensor parallel linear layer with explicit communication.
    """

    def __init__(self, original_linear: nn.Linear, tp_mesh: DeviceMesh, parallel_type: str = "colwise"):
        """
        Initialize tensor parallel linear layer.

        Args:
            original_linear: Original linear layer
            tp_mesh: Device mesh for tensor parallelism
            parallel_type: Type of parallelism ("colwise" or "rowwise")
        """
        super().__init__()
        self.original_linear = original_linear
        self.tp_mesh = tp_mesh
        self.tp_size = tp_mesh.size()
        self.parallel_type = parallel_type
        self.rank = DIST_WRAPPER.rank

        # Apply parallelization based on type
        if parallel_type == "colwise":
            self._apply_colwise_parallel()
        elif parallel_type == "rowwise":
            self._apply_rowwise_parallel()
        else:
            raise ValueError(f"Unsupported parallel_type: {parallel_type}")

    def _apply_colwise_parallel(self):
        """Apply column-wise parallelization."""
        parallelize_plan = {
            "": ColwiseParallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1),
            )
        }

        parallelize_module(
            module=self.original_linear,
            device_mesh=self.tp_mesh,
            parallelize_plan=parallelize_plan,
        )

    def _apply_rowwise_parallel(self):
        """Apply row-wise parallelization."""
        parallelize_plan = {
            "": RowwiseParallel(
                input_layouts=Shard(-1),
                output_layouts=Replicate(),
            )
        }

        parallelize_module(
            module=self.original_linear,
            device_mesh=self.tp_mesh,
            parallelize_plan=parallelize_plan,
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with tensor parallelization."""
        if self.parallel_type == "colwise":
            # For colwise parallel, output is sharded and needs all-gather
            output_sharded = self.original_linear(x)
            return self._all_gather_last_dim(output_sharded)
        else:
            # For rowwise parallel, input should be sharded
            return self.original_linear(x)

    def _all_gather_last_dim(self, tensor: torch.Tensor) -> torch.Tensor:
        """All-gather tensor along the last dimension."""
        if not self.tp_mesh or self.tp_size == 1:
            return tensor

        # All-gather
        gathered_tensors = [torch.empty_like(tensor) for _ in range(self.tp_size)]
        dist.all_gather(gathered_tensors, tensor, group=self.tp_mesh.get_group())

        # Concatenate along last dimension
        output = torch.cat(gathered_tensors, dim=-1)

        return output


class HybridTensorParallelWrapper:
    """
    Combined wrapper that manages both hybrid parallelization and tensor parallelization.

    This wrapper coordinates:
    1. Hybrid parallel execution (pairformer on rank 0, diffusion on all ranks)
    2. Tensor parallel execution (post-pairformer modules across ranks)
    """

    def __init__(self, model, enable_hybrid: bool = True, enable_tp: bool = True, tp_size: int = None):
        """
        Initialize the hybrid tensor parallel wrapper.

        Args:
            model: The Protenix model
            enable_hybrid: Whether to enable hybrid parallelization
            enable_tp: Whether to enable tensor parallelization
            tp_size: Tensor parallel size
        """
        self.model = model
        self.enable_hybrid = enable_hybrid and DIST_WRAPPER.world_size > 1
        self.enable_tp = enable_tp and DIST_WRAPPER.world_size > 1
        self.rank = DIST_WRAPPER.rank
        self.world_size = DIST_WRAPPER.world_size
        self.tp_size = tp_size or self.world_size

        # Initialize hybrid parallel wrapper if available
        if hasattr(model, 'hybrid_parallel'):
            self.hybrid_parallel = model.hybrid_parallel
        else:
            self.hybrid_parallel = None

        # Initialize tensor parallel wrapper
        if self.enable_tp:
            self.tensor_parallel = TensorParallelWrapper(model, enable_tp, tp_size)
        else:
            self.tensor_parallel = None

    def run_post_pairformer_with_tp(
        self,
        s_inputs: torch.Tensor,
        s_trunk: torch.Tensor,
        z_trunk: torch.Tensor,
        input_feature_dict: Dict[str, Any],
        pred_dict: Dict[str, torch.Tensor],
        **kwargs
    ) -> Dict[str, torch.Tensor]:
        """
        Run post-pairformer modules with tensor parallelization.

        Args:
            s_inputs: Single embeddings from input
            s_trunk: Single embeddings from pairformer
            z_trunk: Pair embeddings from pairformer
            input_feature_dict: Input features
            pred_dict: Prediction dictionary to update
            **kwargs: Additional arguments

        Returns:
            Updated prediction dictionary
        """
        # Distogram computation with tensor parallelization
        if self.enable_tp and hasattr(self.model, 'distogram_head'):
            # Use tensor parallel distogram head
            distogram_logits = self.model.distogram_head(z_trunk)
        else:
            # Use regular distogram head
            distogram_logits = self.model.distogram_head(z_trunk)

        # Compute contact probabilities
        from protenix.model import sample_confidence
        pred_dict["contact_probs"] = sample_confidence.compute_contact_prob(
            distogram_logits=distogram_logits,
            **sample_confidence.get_bin_params(self.model.configs.loss.distogram),
        )

        # Confidence computation with tensor parallelization
        if self.enable_tp and hasattr(self.model, 'confidence_head'):
            # Use tensor parallel confidence head
            (
                pred_dict["plddt"],
                pred_dict["pae"],
                pred_dict["pde"],
                pred_dict["resolved"],
            ) = self.model.run_confidence_head(
                input_feature_dict=input_feature_dict,
                s_inputs=s_inputs,
                s_trunk=s_trunk,
                z_trunk=z_trunk,
                pair_mask=None,
                x_pred_coords=pred_dict["coordinate"],
                **kwargs
            )
        else:
            # Use regular confidence head
            (
                pred_dict["plddt"],
                pred_dict["pae"],
                pred_dict["pde"],
                pred_dict["resolved"],
            ) = self.model.run_confidence_head(
                input_feature_dict=input_feature_dict,
                s_inputs=s_inputs,
                s_trunk=s_trunk,
                z_trunk=z_trunk,
                pair_mask=None,
                x_pred_coords=pred_dict["coordinate"],
                **kwargs
            )

        return pred_dict
