# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import logging
import os, sys
import traceback
import urllib.request
import time
import json
from contextlib import nullcontext
from os.path import exists as opexists
from os.path import join as opjoin
from typing import Any, Mapping
import pickle
import gzip
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print("base_dir:", base_dir)
sys.path.insert(0, base_dir)

import torch
import torch.distributed as dist
from configs.configs_base import configs as configs_base
from configs.configs_data import data_configs
from configs.configs_inference import inference_configs
from runner.dumper import DataDumper
from runner.msa_search import contain_msa_res, msa_search_update
from runner.dump_manager import lock_manager, pid_issame

from protenix.config import parse_configs, parse_sys_args
from protenix.data.infer_data_pipeline import get_inference_dataloader
from protenix.model.protenix import Protenix
from protenix.utils.distributed import DIST_WRAPPER
from protenix.utils.seed import seed_everything
from protenix.utils.torch_utils import to_device
from protenix.web_service.dependency_url import URL
from protenix.utils.file_io import load_gzip_pickle

logger = logging.getLogger(__name__)


class TimerContext:
    """Context manager for timing code execution."""
    
    def __init__(self, name, timer_dict):
        self.name = name
        self.timer_dict = timer_dict
        
    def __enter__(self):
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        elapsed_time = time.time() - self.start_time
        if self.name in self.timer_dict:
            self.timer_dict[self.name] += elapsed_time
        else:
            self.timer_dict[self.name] = elapsed_time


class ProfilerInferenceRunner(object):
    def __init__(self, configs: Any) -> None:
        self.configs = configs
        self.timer_dict = {}  # Dictionary to store timing information
        
        with TimerContext("init_env", self.timer_dict):
            self.init_env()
        
        with TimerContext("init_basics", self.timer_dict):
            self.init_basics()
        
        with TimerContext("init_model", self.timer_dict):
            self.init_model()
        
        with TimerContext("load_checkpoint", self.timer_dict):
            self.load_checkpoint()
        
        with TimerContext("init_dumper", self.timer_dict):
            self.init_dumper(need_atom_confidence=configs.need_atom_confidence)

    def init_env(self) -> None:
        self.print(
            f"Distributed environment: world size: {DIST_WRAPPER.world_size}, "
            + f"global rank: {DIST_WRAPPER.rank}, local rank: {DIST_WRAPPER.local_rank}"
        )
        self.use_cuda = torch.cuda.device_count() > 0
        if self.use_cuda:
            self.device = torch.device("cuda:{}".format(DIST_WRAPPER.local_rank))
            os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
            all_gpu_ids = ",".join(str(x) for x in range(torch.cuda.device_count()))
            devices = os.getenv("CUDA_VISIBLE_DEVICES", all_gpu_ids)
            logging.info(
                f"LOCAL_RANK: {DIST_WRAPPER.local_rank} - CUDA_VISIBLE_DEVICES: [{devices}]"
            )
            torch.cuda.set_device(self.device)
        else:
            self.device = torch.device("cpu")
        if DIST_WRAPPER.world_size > 1:
            dist.init_process_group(backend="nccl")
        if self.configs.use_deepspeed_evo_attention:
            env = os.getenv("CUTLASS_PATH", None)
            self.print(f"env: {env}")
            assert (
                env is not None
            ), "if use ds4sci, set `CUTLASS_PATH` env as https://www.deepspeed.ai/tutorials/ds4sci_evoformerattention/"
            if env is not None:
                logging.info(
                    "The kernels will be compiled when DS4Sci_EvoformerAttention is called for the first time."
                )
        use_fastlayernorm = os.getenv("LAYERNORM_TYPE", None)
        if use_fastlayernorm == "fast_layernorm":
            logging.info(
                "The kernels will be compiled when fast_layernorm is called for the first time."
            )

        logging.info("Finished init ENV.")

    def init_basics(self) -> None:
        self.dump_dir = self.configs.dump_dir
        self.error_dir = opjoin(self.dump_dir, "ERR")
        os.makedirs(self.dump_dir, exist_ok=True)
        os.makedirs(self.error_dir, exist_ok=True)
        
        # Create a directory for timing information
        self.timing_dir = opjoin(self.dump_dir, "timing")
        os.makedirs(self.timing_dir, exist_ok=True)

    def init_model(self) -> None:
        self.model = Protenix(self.configs).to(self.device)

    def load_checkpoint(self) -> None:
        checkpoint_path = self.configs.load_checkpoint_path
        if not os.path.exists(checkpoint_path):
            raise Exception(f"Given checkpoint path not exist [{checkpoint_path}]")
        self.print(
            f"Loading from {checkpoint_path}, strict: {self.configs.load_strict}"
        )
        checkpoint = torch.load(checkpoint_path, self.device)

        sample_key = [k for k in checkpoint["model"].keys()][0]
        self.print(f"Sampled key: {sample_key}")
        if sample_key.startswith("module."):  # DDP checkpoint has module. prefix
            checkpoint["model"] = {
                k[len("module.") :]: v for k, v in checkpoint["model"].items()
            }
        self.model.load_state_dict(
            state_dict=checkpoint["model"],
            strict=True,
        )
        self.model.eval()
        self.print(f"Finish loading checkpoint.")

    def init_dumper(self, need_atom_confidence: bool = False):
        self.dumper = DataDumper(
            base_dir=self.dump_dir, need_atom_confidence=need_atom_confidence
        )

    # Adapted from runner.train.Trainer.evaluate
    @torch.no_grad()
    def predict(self, data: Mapping[str, Mapping[str, Any]]) -> dict[str, torch.Tensor]:
        module_timers = {}
        
        eval_precision = {
            "fp32": torch.float32,
            "bf16": torch.bfloat16,
            "fp16": torch.float16,
        }[self.configs.dtype]

        enable_amp = (
            torch.autocast(device_type="cuda", dtype=eval_precision)
            if torch.cuda.is_available()
            else nullcontext()
        )

        with TimerContext("data_to_device", module_timers):
            data = to_device(data, self.device)
            
        with enable_amp:
            # Time each major component of the model
            with TimerContext("model_forward_total", module_timers):
                # Hook into the model's forward method to time individual modules
                original_input_embedder_forward = self.model.input_embedder.forward
                original_template_embedder_forward = self.model.template_embedder.forward if hasattr(self.model, 'template_embedder') else None
                original_msa_module_forward = self.model.msa_module.forward if hasattr(self.model, 'msa_module') else None
                original_pairformer_stack_forward = self.model.pairformer_stack.forward
                original_diffusion_module_forward = self.model.diffusion_module.forward
                original_confidence_head_forward = self.model.confidence_head.forward if hasattr(self.model, 'confidence_head') else None
                
                # Define wrapper functions to time each module
                def timed_input_embedder_forward(*args, **kwargs):
                    with TimerContext("input_embedder", module_timers):
                        return original_input_embedder_forward(*args, **kwargs)
                
                def timed_template_embedder_forward(*args, **kwargs):
                    with TimerContext("template_embedder", module_timers):
                        return original_template_embedder_forward(*args, **kwargs)
                
                def timed_msa_module_forward(*args, **kwargs):
                    with TimerContext("msa_module", module_timers):
                        return original_msa_module_forward(*args, **kwargs)
                
                def timed_pairformer_stack_forward(*args, **kwargs):
                    with TimerContext("pairformer_stack", module_timers):
                        return original_pairformer_stack_forward(*args, **kwargs)
                
                def timed_diffusion_module_forward(*args, **kwargs):
                    with TimerContext("diffusion_module", module_timers):
                        return original_diffusion_module_forward(*args, **kwargs)
                
                def timed_confidence_head_forward(*args, **kwargs):
                    with TimerContext("confidence_head", module_timers):
                        return original_confidence_head_forward(*args, **kwargs)
                
                # Replace the original forward methods with timed versions
                self.model.input_embedder.forward = timed_input_embedder_forward
                if original_template_embedder_forward:
                    self.model.template_embedder.forward = timed_template_embedder_forward
                if original_msa_module_forward:
                    self.model.msa_module.forward = timed_msa_module_forward
                self.model.pairformer_stack.forward = timed_pairformer_stack_forward
                self.model.diffusion_module.forward = timed_diffusion_module_forward
                if original_confidence_head_forward:
                    self.model.confidence_head.forward = timed_confidence_head_forward
                
                # Run the model
                prediction, _, _ = self.model(
                    input_feature_dict=data["input_feature_dict"],
                    label_full_dict=None,
                    label_dict=None,
                    mode="inference",
                )
                
                # Restore the original forward methods
                self.model.input_embedder.forward = original_input_embedder_forward
                if original_template_embedder_forward:
                    self.model.template_embedder.forward = original_template_embedder_forward
                if original_msa_module_forward:
                    self.model.msa_module.forward = original_msa_module_forward
                self.model.pairformer_stack.forward = original_pairformer_stack_forward
                self.model.diffusion_module.forward = original_diffusion_module_forward
                if original_confidence_head_forward:
                    self.model.confidence_head.forward = original_confidence_head_forward

        # Update the main timer dictionary with module timers
        for key, value in module_timers.items():
            self.timer_dict[key] = self.timer_dict.get(key, 0) + value

        return prediction

    def print(self, msg: str):
        if DIST_WRAPPER.rank == 0:
            logger.info(msg)

    def update_model_configs(self, new_configs: Any) -> None:
        self.model.configs = new_configs
        
    def save_timing_info(self, sample_name: str, complex_info: dict):
        """Save timing information to a JSON file."""
        # Save timing info on the rank that processed this sample
        timing_file = opjoin(self.timing_dir, f"{sample_name}_timing.json")

        # Combine timing information with complex information
        timing_data = {
            "sample_name": sample_name,
            "complex_info": complex_info,
            "timing": self.timer_dict,
            "processed_by_rank": DIST_WRAPPER.rank  # Track which rank processed this
        }

        with open(timing_file, 'w') as f:
            json.dump(timing_data, f, indent=2)

        logger.info(f"Timing information saved to {timing_file}")

        # Create a summary file with all timings (use file locking for multi-rank safety)
        summary_file = opjoin(self.timing_dir, "timing_summary.json")

        # Use file locking to safely update summary across multiple ranks
        import fcntl

        # Add current sample data
        sample_summary = {
            "sample_name": sample_name,
            "complex_info": complex_info,
            "timing": self.timer_dict,
            "processed_by_rank": DIST_WRAPPER.rank
        }

        # Safely update summary file with file locking
        try:
            # Open file for read/write, create if doesn't exist
            with open(summary_file, 'a+') as f:
                fcntl.flock(f.fileno(), fcntl.LOCK_EX)  # Exclusive lock
                f.seek(0)

                # Load existing data
                try:
                    content = f.read()
                    if content.strip():
                        summary_data = json.loads(content)
                    else:
                        summary_data = {"samples": []}
                except (json.JSONDecodeError, ValueError):
                    summary_data = {"samples": []}

                # Check if sample already exists and update it
                sample_exists = False
                for i, sample in enumerate(summary_data["samples"]):
                    if sample["sample_name"] == sample_name:
                        summary_data["samples"][i] = sample_summary
                        sample_exists = True
                        break

                if not sample_exists:
                    summary_data["samples"].append(sample_summary)

                # Write updated data
                f.seek(0)
                f.truncate()
                json.dump(summary_data, f, indent=2)

        except Exception as e:
            logger.warning(f"Failed to update summary file: {e}")
            # Fallback: create individual summary file for this rank
            rank_summary_file = opjoin(self.timing_dir, f"timing_summary_rank_{DIST_WRAPPER.rank}.json")
            with open(rank_summary_file, 'w') as f:
                json.dump({"samples": [sample_summary]}, f, indent=2)

        # Reset timer for next sample
        self.timer_dict = {}


def download_infercence_cache(configs: Any, model_version: str = "v0.2.0") -> None:
    current_file_path = os.path.abspath(__file__)
    current_directory = os.path.dirname(current_file_path)
    code_directory = os.path.dirname(current_directory)

    data_cache_dir = os.path.join(code_directory, "release_data/ccd_cache")
    os.makedirs(data_cache_dir, exist_ok=True)
    for cache_name, fname in [
        ("ccd_components_file", "components.v20240608.cif"),
        ("ccd_components_rdkit_mol_file", "components.v20240608.cif.rdkit_mol.pkl"),
    ]:
        if not opexists(cache_path := os.path.abspath(opjoin(data_cache_dir, fname))):
            tos_url = URL[cache_name]
            logger.info(f"Downloading data cache from\n {tos_url}...")
            urllib.request.urlretrieve(tos_url, cache_path)

    checkpoint_path = configs.load_checkpoint_path

    if not opexists(checkpoint_path):
        checkpoint_path = os.path.join(
            code_directory, f"release_data/checkpoint/model_{model_version}.pt"
        )
        os.makedirs(os.path.dirname(checkpoint_path), exist_ok=True)
        tos_url = URL[f"model_{model_version}"]
        logger.info(f"Downloading model checkpoint from\n {tos_url}...")
        urllib.request.urlretrieve(tos_url, checkpoint_path)
        try:
            ckpt = torch.load(checkpoint_path)
            del ckpt
        except:
            os.remove(checkpoint_path)
            raise RuntimeError(
                "Download model checkpoint failed, please download by yourself with "
                f"wget {tos_url} -O {checkpoint_path}"
            )
        configs.load_checkpoint_path = checkpoint_path


def update_inference_configs(configs: Any, N_token: int):
    # Setting the default inference configs for different N_token and N_atom
    # when N_token is larger than 3000, the default config might OOM even on a
    # A100 80G GPUS,
    if N_token > 3840:
        configs.skip_amp.confidence_head = False
        configs.skip_amp.sample_diffusion = False
    elif N_token > 2560:
        configs.skip_amp.confidence_head = False
        configs.skip_amp.sample_diffusion = True
    else:
        configs.skip_amp.confidence_head = True
        configs.skip_amp.sample_diffusion = True
    return configs


def infer_predict(runner: ProfilerInferenceRunner, configs: Any) -> None:
    # Start timing the entire inference process
    total_start_time = time.time()
    runner.timer_dict["total_inference"] = 0
    
    # update msa result if not contains precomputed msa dir
    with TimerContext("msa_preparation", runner.timer_dict):
        if not contain_msa_res(configs.input_json_path):
            logger.info(
                f"{configs.input_json_path} dose not contain precomputed msa dir, now searching it."
            )
            configs.input_json_path = msa_search_update(
                configs.input_json_path, configs.dump_dir
            )
            logger.info(
                f"msa searching completed, new input json is {configs.input_json_path}"
            )
    
    # Data loading
    with TimerContext("data_loading", runner.timer_dict):
        logger.info(f"Loading data from\n{configs.input_json_path}")
        dataloader = get_inference_dataloader(configs=configs)
        dataset_name = ""
        num_data = len(dataloader.dataset)
    
    for seed in configs.seeds:
        seed_everything(seed=seed, deterministic=True)
        for batch in dataloader:
            try:
                with TimerContext("batch_processing", runner.timer_dict):
                    data, atom_array, data_error_message = batch[0]
                
                if len(data_error_message) > 0:
                    logger.info(data_error_message)
                    with open(
                        opjoin(runner.error_dir, f"{data['sample_name']}.txt"),
                        "w",
                    ) as f:
                        f.write(data_error_message)
                    continue

                sample_name = data["sample_name"]
                
                # Collect complex information
                complex_info = {
                    "sample_name": sample_name,
                    "N_asym": data["N_asym"].item(),
                    "N_token": data["N_token"].item(),
                    "N_atom": data["N_atom"].item(),
                    "N_msa": data["N_msa"].item(),
                    "N_prot_atom": data.get("N_prot_atom", torch.tensor([0])).item(),
                    "N_lig_atom": data.get("N_lig_atom", torch.tensor([0])).item(),
                    "N_dna_atom": data.get("N_dna_atom", torch.tensor([0])).item(),
                    "N_rna_atom": data.get("N_rna_atom", torch.tensor([0])).item(),
                    "N_prot_token": data.get("N_prot_token", torch.tensor([0])).item(),
                    "N_lig_token": data.get("N_lig_token", torch.tensor([0])).item(),
                    "N_dna_token": data.get("N_dna_token", torch.tensor([0])).item(),
                    "N_rna_token": data.get("N_rna_token", torch.tensor([0])).item(),
                }
                
                dump_dir = runner.dumper._get_dump_dir(dataset_name, f"{sample_name}", seed)
                os.system(f"mkdir -p '{dump_dir}'")
                with lock_manager(dump_dir) as pid:
                    if pid_issame(pid, str(os.getpid()).strip()):
                        logger.info(
                            (
                                f"[Rank {DIST_WRAPPER.rank} ({data['sample_index'] + 1}/{num_data})] {sample_name}: "
                                f"N_asym {data['N_asym'].item()}, N_token {data['N_token'].item()}, "
                                f"N_atom {data['N_atom'].item()}, N_msa {data['N_msa'].item()}"
                            )
                        )
                        
                        with TimerContext("config_update", runner.timer_dict):
                            new_configs = update_inference_configs(configs, data["N_token"].item())
                            runner.update_model_configs(new_configs)
                        
                        with TimerContext("prediction", runner.timer_dict):
                            prediction = runner.predict(data)
                        
                        with TimerContext("dumping_results", runner.timer_dict):
                            runner.dumper.dump(
                                dataset_name=dataset_name,
                                pdb_id=sample_name,
                                seed=seed,
                                pred_dict=prediction,
                                atom_array=atom_array,
                                entity_poly_type=data["entity_poly_type"],
                            )
                        
                        # Calculate total time
                        runner.timer_dict["total_inference"] = time.time() - total_start_time
                        
                        # Save timing information
                        runner.save_timing_info(sample_name, complex_info)
                        
                        logger.info(
                            f"[Rank {DIST_WRAPPER.rank}] {data['sample_name']} succeeded.\n"
                            f"Results saved to {configs.dump_dir}"
                        )
                        torch.cuda.empty_cache()
                    else:
                        print(f"skip {sample_name}... ")
            except Exception as e:
                error_message = f"[Rank {DIST_WRAPPER.rank}]{data['sample_name']} {e}:\n{traceback.format_exc()}"
                logger.info(error_message)
                # Save error info
                if opexists(
                    error_path := opjoin(runner.error_dir, f"{sample_name}.txt")
                ):
                    os.remove(error_path)
                with open(error_path, "w") as f:
                    f.write(error_message)
                if hasattr(torch.cuda, "empty_cache"):
                    torch.cuda.empty_cache()
                raise RuntimeError(f"run infer failed: {str(e)}")


def consolidate_timing_summary(timing_dir: str) -> None:
    """Consolidate timing information from all ranks into a single summary file."""
    import glob

    summary_file = opjoin(timing_dir, "timing_summary.json")
    rank_summary_files = glob.glob(opjoin(timing_dir, "timing_summary_rank_*.json"))

    # If we have rank-specific files, merge them
    if rank_summary_files:
        all_samples = []

        # Load from main summary file if it exists
        if os.path.exists(summary_file):
            try:
                with open(summary_file, 'r') as f:
                    main_data = json.load(f)
                    all_samples.extend(main_data.get("samples", []))
            except (json.JSONDecodeError, ValueError):
                pass

        # Load from rank-specific files
        for rank_file in rank_summary_files:
            try:
                with open(rank_file, 'r') as f:
                    rank_data = json.load(f)
                    all_samples.extend(rank_data.get("samples", []))
            except (json.JSONDecodeError, ValueError):
                continue

        # Remove duplicates (keep the latest entry for each sample)
        unique_samples = {}
        for sample in all_samples:
            sample_name = sample.get("sample_name")
            if sample_name:
                unique_samples[sample_name] = sample

        # Write consolidated summary
        consolidated_data = {
            "samples": list(unique_samples.values()),
            "total_samples": len(unique_samples),
            "consolidated_from_ranks": True
        }

        with open(summary_file, 'w') as f:
            json.dump(consolidated_data, f, indent=2)

        # Clean up rank-specific files
        for rank_file in rank_summary_files:
            try:
                os.remove(rank_file)
            except OSError:
                pass

        logger.info(f"Consolidated timing summary saved to {summary_file}")


def main(configs: Any) -> None:
    # Runner
    runner = ProfilerInferenceRunner(configs)
    for name, param in runner.model.named_parameters():
        break
    infer_predict(runner, configs)

    # Consolidate timing information from all ranks (only on rank 0)
    if DIST_WRAPPER.rank == 0:
        consolidate_timing_summary(runner.timing_dir)


def run() -> None:
    LOG_FORMAT = "%(asctime)s,%(msecs)-3d %(levelname)-8s [%(filename)s:%(lineno)s %(funcName)s] %(message)s"
    logging.basicConfig(
        format=LOG_FORMAT,
        level=logging.INFO,
        datefmt="%Y-%m-%d %H:%M:%S",
        filemode="w",
    )
    configs_base["use_deepspeed_evo_attention"] = (
        os.environ.get("USE_DEEPSPEED_EVO_ATTTENTION", False) == "true"
    )
    configs = {**configs_base, **{"data": data_configs}, **inference_configs}
    configs = parse_configs(
        configs=configs,
        arg_str=parse_sys_args(),
        fill_required_with_null=True,
    )
    download_infercence_cache(configs, model_version="v0.2.0")
    main(configs)


if __name__ == "__main__":
    run()