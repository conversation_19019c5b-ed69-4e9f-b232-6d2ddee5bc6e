#!/usr/bin/env python3
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Inference profiler with Tensor Parallel support.

This is a specialized version of inference_profiler.py that enables
tensor parallel optimization for post-pairformer modules.
"""

import os
import sys
import logging

# Add the project root to the path
base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, base_dir)
print(f"base_dir: {base_dir}")

from configs.configs_base import configs as configs_base
from configs.configs_data import data_configs
from configs.configs_inference import inference_configs
from runner.dumper import DataDumper
from runner.msa_search import contain_msa_res, msa_search_update
from runner.dump_manager import lock_manager, pid_issame

from protenix.config import parse_configs, parse_sys_args
from protenix.data.infer_data_pipeline import get_inference_dataloader
from protenix.model.protenix import Protenix
from protenix.utils.distributed import DIST_WRAPPER
from protenix.utils.seed import seed_everything
from protenix.utils.torch_utils import to_device
from protenix.utils.download import download_infercence_cache

logger = logging.getLogger(__name__)


def main(configs):
    """Main inference function with tensor parallel support."""
    # Enable tensor parallel based on environment variables
    enable_tensor_parallel = os.environ.get("ENABLE_TENSOR_PARALLEL", "false").lower() == "true"
    tensor_parallel_size = int(os.environ.get("TENSOR_PARALLEL_SIZE", str(DIST_WRAPPER.world_size)))
    
    # Update configs for tensor parallel
    configs.enable_tensor_parallel = enable_tensor_parallel
    configs.tensor_parallel_size = tensor_parallel_size
    configs.enable_hybrid_parallel = True  # Always enable hybrid parallel
    
    if enable_tensor_parallel:
        logger.info("Tensor Parallel optimization enabled")
        logger.info(f"Tensor parallel size: {tensor_parallel_size}")
        logger.info(f"World size: {DIST_WRAPPER.world_size}")
        logger.info(f"Rank: {DIST_WRAPPER.rank}")
    
    # Set seed
    seed_everything(seed=configs.seeds[0], deterministic=configs.deterministic)
    
    # Create model with tensor parallel support
    model = Protenix(configs)
    
    # Load checkpoint
    if configs.load_checkpoint_path:
        logger.info(f"Loading checkpoint from {configs.load_checkpoint_path}")
        model.load_checkpoint(
            configs.load_checkpoint_path,
            load_strict=configs.load_strict,
            load_params_only=configs.load_params_only,
        )
    
    # Get dataloader
    dataloader = get_inference_dataloader(configs)
    
    # Create dumper
    dumper = DataDumper(configs)
    
    # Run inference
    logger.info("Starting inference with tensor parallel optimization...")
    
    for batch_idx, batch in enumerate(dataloader):
        logger.info(f"Processing batch {batch_idx + 1}/{len(dataloader)}")
        
        # Move batch to device
        batch = to_device(batch, model.device)
        
        # MSA search if needed
        if configs.use_msa and contain_msa_res(batch["input_feature_dict"]):
            batch = msa_search_update(batch, configs)
        
        # Run inference
        with lock_manager(batch["input_feature_dict"]["pdb_id"][0]):
            if pid_issame(batch["input_feature_dict"]["pdb_id"][0], configs.dump_dir):
                logger.info(f"Skipping {batch['input_feature_dict']['pdb_id'][0]} (already processed)")
                continue
                
            # Forward pass
            prediction, timing_info, _ = model(
                input_feature_dict=batch["input_feature_dict"],
                label_full_dict=None,
                label_dict=None,
                mode="inference",
            )
            
            # Dump results
            dumper.dump_prediction(
                prediction=prediction,
                timing_info=timing_info,
                input_feature_dict=batch["input_feature_dict"],
                batch_idx=batch_idx,
            )
            
            logger.info(f"Completed batch {batch_idx + 1}, timing: {timing_info}")
    
    logger.info("Inference completed successfully!")


def run():
    """Main entry point."""
    LOG_FORMAT = "%(asctime)s,%(msecs)-3d %(levelname)-8s [%(filename)s:%(lineno)s %(funcName)s] %(message)s"
    logging.basicConfig(
        format=LOG_FORMAT,
        level=logging.INFO,
        datefmt="%Y-%m-%d %H:%M:%S",
        filemode="w",
    )
    
    # Set up configs
    configs_base["use_deepspeed_evo_attention"] = (
        os.environ.get("USE_DEEPSPEED_EVO_ATTTENTION", False) == "true"
    )
    configs = {**configs_base, **{"data": data_configs}, **inference_configs}
    configs = parse_configs(
        configs=configs,
        arg_str=parse_sys_args(),
        fill_required_with_null=True,
    )
    
    # Download inference cache
    download_infercence_cache(configs, model_version="v0.2.0")
    
    # Run main function
    main(configs)


if __name__ == "__main__":
    run()
